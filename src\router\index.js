// src/router/index.js
// Handles browser history and restoring saved state or tank selection.
import { updateFilterSelection } from '../components/tank-list/index.js';
import { state } from '../store/state.js';

// Initialize router: restore state and attach popstate handler
export function initRouter({ initializeAllTanksCache: _initializeAllTanksCache, cacheDOMElements, handleTankSelection, applyFiltersAndRenderTankList }) {
  // restore saved state
  loadSavedState({ cacheDOMElements, handleTankSelection, applyFiltersAndRenderTankList });

  window.addEventListener('popstate', (_event) => {
    const hash = window.location.hash;
    const tankName = hash ? decodeURIComponent(hash.substring(1)) : null;

    if (tankName) {
      if (!state.allTanks.length) return; // not ready
      const tank = state.allTanks.find(t => t.name === tankName);
      if (tank) {
        handleTankSelection(tankName, true);
      } else {
        clearSelection(applyFiltersAndRenderTankList);
      }
    } else {
      clearSelection(applyFiltersAndRenderTankList);
    }
  });
}

function clearSelection(applyFiltersAndRenderTankList) {
  state.selectedTank = null;
  localStorage.removeItem('selectedTank');
  state.selectedCountry = 'all';
  state.selectedCategory = 'all';
  updateFilterSelection('country', 'all');
  updateFilterSelection('category', 'all');
  applyFiltersAndRenderTankList();
}

export function loadSavedState({ cacheDOMElements, handleTankSelection, applyFiltersAndRenderTankList }) {
  const currentTab = localStorage.getItem('activeMenuItem') || 'overview';
  if (currentTab !== 'vehicles') return;

  const savedTankDataStr = localStorage.getItem('selectedTank');
  if (!savedTankDataStr) return;

  try {
    const savedTank = JSON.parse(savedTankDataStr);
    state.selectedCountry = savedTank.country || 'all';
    const internalType = savedTank.type ? mapEngTypeToInternal(savedTank.type) : 'all';
    state.selectedCategory = internalType;

    cacheDOMElements();
    updateFilterSelection('country', state.selectedCountry);
    if (savedTank.type) updateFilterSelection('category', savedTank.type);

    // Ensure tanks cache is ready
    applyFiltersAndRenderTankList(true);

    if (savedTank.name) {
      handleTankSelection(savedTank.name, true);
    }
  } catch (e) {
    console.error('Failed to parse saved tank data', e);
  }
}

function mapEngTypeToInternal(eng) {
  const map = {
    'HT': 'heavyTank',
    'MT': 'mediumTank',
    'LT': 'lightTank',
    'SPG': 'spg',
    'TD': 'at-spg',
  };
  return map[eng] || eng;
}
