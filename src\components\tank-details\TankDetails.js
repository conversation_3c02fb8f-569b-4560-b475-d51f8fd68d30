import { renderBuildsTable } from '../builds/BuildsTable.js';
import {
  getTankIconPath,
  getFlagPath,
  getBlurredFlagPath,
  getRoleIconPath,
  getTankTypeClass
} from '../../utils/constants.js';

// Переменные для отслеживания скролла (глобальные для модуля)
let scrollTimeouts = [];
let userScrolledManually = false;
let lastScrollPosition = 0;
let programmaticScrollInProgress = false;

// Функция для плавной коррекции скролла при изменении размера страницы
function smoothScrollCorrection(targetPosition, duration = 300) {
  const startPosition = window.pageYOffset;
  const distance = targetPosition - startPosition;
  const startTime = performance.now();

  function animation(currentTime) {
    const elapsed = currentTime - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // Используем easing функцию для плавности
    const easeProgress = 1 - Math.pow(1 - progress, 3);

    const currentPosition = startPosition + (distance * easeProgress);
    window.scrollTo(0, currentPosition);

    if (progress < 1) {
      requestAnimationFrame(animation);
    }
  }

  requestAnimationFrame(animation);
}

// Функция для очистки всех отложенных скроллов
function clearScrollTimeouts() {
  scrollTimeouts.forEach(timeout => clearTimeout(timeout));
  scrollTimeouts = [];
}

// Функция для отслеживания ручного скролла пользователя
function setupScrollTracking() {
  // Удаляем предыдущий обработчик, если есть
  window.removeEventListener('scroll', handleUserScroll);

  // Добавляем новый обработчик
  window.addEventListener('scroll', handleUserScroll, { passive: true });
}

function handleUserScroll() {
  // Игнорируем события скролла во время программного скролла
  if (programmaticScrollInProgress) {
    return;
  }

  const currentScrollPosition = window.pageYOffset;

  // Если пользователь прокрутил страницу вручную (изменение больше 10px)
  // Увеличили порог, чтобы избежать ложных срабатываний от программного скролла
  if (Math.abs(currentScrollPosition - lastScrollPosition) > 10) {
    userScrolledManually = true;
    // Отменяем все отложенные скроллы
    clearScrollTimeouts();
    console.log('👆 ОБНАРУЖЕН РУЧНОЙ СКРОЛЛ, отменяем автоскролл');
  }

  lastScrollPosition = currentScrollPosition;
}

// Инициализация компонента деталей танка
export function initializeTankDetails() {
  // Создаем обработчики для переключения вкладок
  initializeTabHandlers();

  // Инициализируем подсказки
  initializeTooltips();
}

// Обновление UI деталей танка
export function updateTankDetailsUI(tankObject) {
  if (!tankObject) {
    console.error("updateTankDetailsUI: tankObject is null or undefined");
    hideTankDetails();
    return;
  }

  const container = document.getElementById('tank-characteristics-container');
  if (!container) {
    console.error("Tank characteristics container not found!");
    return;
  }

  // Очищаем предыдущие обработчики скролла при смене танка
  if (typeof clearScrollTimeouts === 'function') {
    clearScrollTimeouts();
  }
  window.removeEventListener('scroll', handleUserScroll);

  // Создаем полную HTML структуру для страницы характеристик
  renderTankCharacteristicsPage(container, tankObject);

  // Показываем контейнер только после полной загрузки
  setTimeout(() => {
    showTankDetails();
    setupTooltips();
  }, 50); // Минимальная задержка для предотвращения мелькания
}

// Рендеринг полной страницы характеристик танка
function renderTankCharacteristicsPage(container, tank) {
  // Получаем правильные пути к изображениям
  const tankIconPath = getTankIconPath(tank.name);
  const flagPath = getFlagPath(tank.country);

  // Правильно извлекаем имя роли из объекта - роли уже правильно отформатированы в tanks.js
  const roleName = tank.role?.name || 'Универсальный';
  const roleIconPath = getRoleIconPath(roleName);

  console.log('Role data:', {
    rawRole: tank.role,
    roleName: roleName,
    roleIconPath: roleIconPath
  });

  // Создаем HTML для страницы характеристик
  container.innerHTML = `
    <div class="tank-characteristics-page">
      <!-- Кнопка назад -->
      <div class="back-button-container">
        <button class="back-to-list-btn" onclick="history.back()">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
          Назад к списку
        </button>
      </div>

      <!-- Заголовок танка -->
      <div class="tank-header" data-tank-type="${getTankTypeClass(tank.type)}">
        <div class="tank-header-left">
          <div class="tank-icon-container">
            <img src="${tankIconPath}" alt="${tank.name}" class="tank-icon-large"
                 onerror="this.src='src/assets/images/tanks/maus.webp'; this.onerror=null;">
          </div>
          <div class="tank-info">
            <div class="tank-name-row">
              <img src="${flagPath}" alt="${tank.country}" class="tank-flag" onerror="this.style.display='none'">
              <div class="tank-name-info">
                <div class="tank-name-with-badge">
                  <h1 class="tank-name">${tank.name}</h1>
                  <span class="tank-type-badge ${getTankTypeClass(tank.type)}">${tank.type}</span>
                </div>
                <div class="tank-meta">
                  <div class="tank-role">
                    <img src="${roleIconPath}" alt="role" class="role-icon" onerror="this.style.display='none'">
                    <span class="role-text">${roleName}</span>
                  </div>
                  ${tank.cost ? `<div class="tank-price">
                    <img src="${tank.currencyIcon || 'src/assets/images/role/silver.png'}" alt="currency" class="currency-icon" onerror="this.style.display='none'">
                    <span class="price-value">${tank.cost.toLocaleString()}</span>
                  </div>` : ''}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tank-header-right">
          <div class="equipment-slots">
            <!-- Группа 1: Слот 1 (отдельно) -->
            <div class="equipment-slot slot-1" id="equipment-slot-1" title="Слот 1">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 2: Слоты 2-3-4 (в притык) -->
            <div class="equipment-slot slot-2" id="equipment-slot-2" title="Слот 2">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-3" id="equipment-slot-3" title="Слот 3">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-4" id="equipment-slot-4" title="Слот 4">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 3: Слот 5 (отдельно) -->
            <div class="equipment-slot slot-5" id="equipment-slot-5" title="Слот 5">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 4: Слоты 6-7-8 (в притык) -->
            <div class="equipment-slot slot-6" id="equipment-slot-6" title="Слот 6">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-7" id="equipment-slot-7" title="Слот 7">
              <div class="slot-background"></div>
            </div>
            <div class="equipment-slot slot-8" id="equipment-slot-8" title="Слот 8">
              <div class="slot-background"></div>
            </div>

            <!-- Группа 5: Слот 9 (отдельно) -->
            <div class="equipment-slot slot-9" id="equipment-slot-9" title="Слот 9">
              <div class="slot-background"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- НОВЫЙ LAYOUT: Характеристики сверху -->
      <div class="characteristics-main-layout">
        <div class="characteristics-sections">
          <!-- Вооружение (красная секция) -->
          <div class="characteristics-section armament">
            <div class="section-header">
              <h2>🔥 Вооружение</h2>
            </div>
            <div class="characteristics-grid">
              ${renderCharacteristicItem('Урон в минуту', tank.characteristics?.dpm || tank.characteristics?.damagePerMinute || 'Н/Д', 'dpm')}
              ${renderCharacteristicItem('Урон', tank.characteristics?.damage || 'Н/Д', 'damage')}
              ${renderCharacteristicItem('Бронепробитие (мм)', tank.characteristics?.penetration || 'Н/Д', 'penetration')}
              ${renderCharacteristicItem('Время перезарядки (сек)', tank.characteristics?.reloadTime || 'Н/Д', 'reload')}
              ${renderCharacteristicItem('Скорострельность (выстр/мин)', tank.characteristics?.rateOfFire || 'Н/Д', 'rof')}
              ${renderCharacteristicItem('Время сведения (сек)', tank.characteristics?.aimTime || 'Н/Д', 'aim')}
              ${renderCharacteristicItem('Разброс (м)', tank.characteristics?.dispersion || 'Н/Д', 'dispersion')}
            </div>
          </div>

          <!-- Мобильность (бирюзовая секция) -->
          <div class="characteristics-section mobility">
            <div class="section-header">
              <h2>⚡ Мобильность</h2>
            </div>
            <div class="characteristics-grid">
              ${renderCharacteristicItem('Максимальная скорость (км/ч)', tank.characteristics?.speed || tank.characteristics?.maxSpeed || 'Н/Д', 'speed')}
              ${renderCharacteristicItem('Скорость заднего хода (км/ч)', tank.characteristics?.reverseSpeed || 'Н/Д', 'reverse')}
              ${renderCharacteristicItem('Мощность двигателя (л.с.)', tank.characteristics?.enginePower || 'Н/Д', 'engine')}
              ${renderCharacteristicItem('Удельная мощность (л.с./т)', tank.characteristics?.powerToWeight || 'Н/Д', 'power')}
              ${renderCharacteristicItem('Скорость поворота (°/сек)', tank.characteristics?.traverse || tank.characteristics?.traverseSpeed || 'Н/Д', 'traverse')}
            </div>
          </div>

          <!-- Прочие характеристики (зеленая секция) -->
          <div class="characteristics-section other">
            <div class="section-header">
              <h2>✅ Прочие</h2>
            </div>
            <div class="characteristics-grid">
              ${renderCharacteristicItem('Прочность', tank.characteristics?.hitPoints || 'Н/Д', 'hp')}
              ${renderCharacteristicItem('Обзор (м)', tank.characteristics?.viewRange || 'Н/Д', 'view')}
              ${renderCharacteristicItem('Дальность связи (м)', tank.characteristics?.radioRange || 'Н/Д', 'radio')}
              ${renderCharacteristicItem('Шанс возгорания (%)', tank.characteristics?.fireChance || 'Н/Д', 'fire')}
              ${renderCharacteristicItem('Броня корпуса (мм)', tank.characteristics?.hullArmor?.front || 'Н/Д', 'hull')}
              ${renderCharacteristicItem('Броня башни (мм)', tank.characteristics?.turretArmor?.front || 'Н/Д', 'turret')}
            </div>
          </div>
        </div>
      </div>

      <!-- Сборки и примечания снизу -->
      <div class="builds-and-notes-section">
        <div class="builds-and-notes-column">
          <!-- Таблица сборок -->
          <div class="builds-section">
        <div class="builds-header" style="display: none;">
          <h3 id="builds-message" style="display: none;"></h3>
        </div>
        <div class="builds-table-container">
          <table class="builds-table-daisy table table-zebra w-full">
            <thead>
              <tr>
                <th class="th-build">
                  <div style="display: flex; align-items: center; gap: 8px;">
                    <svg
                      id="toggle-notes-icon"
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="#ff3b30"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      style="cursor: pointer; transition: all 0.3s ease;"
                      onclick="toggleNotesSection()"
                      title="Показать/скрыть примечания"
                    >
                      <path d="M12 9v4" />
                      <path d="M10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z" />
                      <path d="M12 16h.01" />
                    </svg>
                    <span>Сборка</span>
                  </div>
                </th>
                <th class="th-efficiency sortable" onclick="sortBuildsTable('efficiency', this)">
                  Эффективность
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-contribution sortable" onclick="sortBuildsTable('contribution', this)">
                  Вклад
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-damage sortable" onclick="sortBuildsTable('damage', this)">
                  Урон
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-assist sortable" onclick="sortBuildsTable('assist', this)">
                  Помощь
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-dpm sortable" onclick="sortBuildsTable('dpm', this)">
                  DPM
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-spread sortable" onclick="sortBuildsTable('spread', this)">
                  Разброс
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-dynspread sortable" onclick="sortBuildsTable('dynSpread', this)">
                  Дин. разброс
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-aimtime sortable" onclick="sortBuildsTable('aimTime', this)">
                  Сведение (с)
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-viewrange sortable" onclick="sortBuildsTable('viewRange', this)">
                  Обзор (м)
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-dispersion sortable" onclick="sortBuildsTable('dispersion', this)">
                  Дисперсия
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-cv sortable" onclick="sortBuildsTable('cv', this)">
                  CV (%)
                  <span class="sort-arrow"></span>
                </th>
                <th class="th-accuracy sortable" onclick="sortBuildsTable('accuracy', this)">
                  Точность (%)
                  <span class="sort-arrow"></span>
                </th>
              </tr>
            </thead>
            <tbody id="builds-table-body" class="text-xs">
            </tbody>
          </table>
          </div>

          <!-- Секция примечаний -->
          <div class="notes-section">
            <div class="notes-header">
              <div class="notes-title">
                <svg xmlns="http://www.w3.org/2000/svg" class="notes-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                  <polyline points="14,2 14,8 20,8"/>
                  <line x1="16" y1="13" x2="8" y2="13"/>
                  <line x1="16" y1="17" x2="8" y2="17"/>
                  <polyline points="10,9 9,9 8,9"/>
                </svg>
                <h3>Примечания</h3>
              </div>
            </div>
            <div class="notes-content-compact">
              <div class="note-item-compact">
                <div class="note-text-compact"><strong>Постоянный бонус:</strong> Процент или фиксированное значение, добавляемое к базовым характеристикам танка.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-efficiency">Эффективность:</strong> Оценка сборки (0-100) по весам: Вклад (30%), Урон (25%), Помощь (20%), DPM (15%), Обзор (10%), CV (5%).</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-contribution">Вклад:</strong> Суммарный вклад в бой (урон + помощь в нанесении урона + блокированный урон + разведка).</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-damage">Урон:</strong> Средний урон за бой. Основной показатель эффективности в нанесении урона противнику.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-help">Помощь:</strong> Урон по засвеченным целям + блокированный урон + урон по оглушенным целям.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-dpm">DPM:</strong> Средний урон в минуту (Damage Per Minute) - расчетный показатель потенциального урона.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-spread">Разброс:</strong> Показатель неточности орудия между выстрелами - чем меньше значение, тем выше точность стрельбы.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-dynspread">Дин. разброс:</strong> Дополнительный разброс при движении танка - влияет на точность стрельбы в движении.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-aimtime">Сведение (с):</strong> Время полного сведения прицела после выстрела или поворота башни в секундах.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-viewrange">Обзор (м):</strong> Дальность обнаружения противника в метрах - влияет на способность засвечивать вражеские танки.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-dispersion">Дисперсия:</strong> Коэффициент разброса снарядов - влияет на кучность стрельбы и стабильность орудия.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-cv">CV (%):</strong> Коэффициент вариации в процентах - показатель стабильности результатов, чем меньше значение, тем стабильнее показатели.</div>
              </div>
              <div class="note-item-compact">
                <div class="note-text-compact"><strong class="category-accuracy">Точность (%):</strong> Процентный показатель попаданий по цели - отражает общую эффективность стрельбы.</div>
              </div>
            </div>
          </div>
        </div> <!-- Закрываем builds-and-notes-column -->
      </div> <!-- Закрываем builds-and-notes-section -->
    </div>


  `;

  // После рендеринга HTML, обновляем таблицу сборок
  setTimeout(() => {
    const buildsTableBody = document.getElementById('builds-table-body');

    if (buildsTableBody && tank) {
      const hasBuilds = tank.bestBuilds && tank.bestBuilds.length > 0;

      if (hasBuilds) {
        // Сохраняем текущий танк для сортировки
        currentTankBuilds = tank;
        // Сбрасываем состояние сортировки
        currentSortColumn = null;
        currentSortDirection = 'desc';
        // Если есть сборки, показываем таблицу
        renderBuildsTable(tank);
      } else {
        // Если нет сборок, просто очищаем таблицу (без сообщения)
        buildsTableBody.innerHTML = '';
      }
    }
  }, 100);
}

// Вспомогательная функция для рендеринга элемента характеристики
function renderCharacteristicItem(label, value, type) {
  const percentage = getCharacteristicPercentage(value, type);
  const displayValue = formatCharacteristicValue(value, type);

  // Определяем, является ли значение низким (менее 30%)
  const isLowValue = percentage < 30 && percentage > 0;
  const lowValueClass = isLowValue ? ' low-value' : '';

  return `
    <div class="characteristic-item" data-type="${type}">
      <div class="characteristic-label">${label}</div>
      <div class="characteristic-value-container">
        <div class="characteristic-bar">
          <div class="characteristic-fill${lowValueClass}" style="width: ${percentage}%"></div>
        </div>
        <div class="characteristic-value">${displayValue}</div>
      </div>
    </div>
  `;
}

// Функция для получения процента заполнения полоски характеристики
function getCharacteristicPercentage(value, type) {
  // Если значение "Н/Д", возвращаем 0
  if (value === 'Н/Д' || value === null || value === undefined) {
    return 0;
  }

  const numericValue = parseFloat(value.toString().replace(/[^\d.]/g, ''));
  if (isNaN(numericValue)) return 0;

  // Нормализуем значения для разных типов характеристик
  switch (type) {
    // Вооружение
    case 'dpm': return Math.min(numericValue / 4000 * 100, 100);
    case 'damage': return Math.min(numericValue / 600 * 100, 100);
    case 'penetration': return Math.min(numericValue / 350 * 100, 100);
    case 'reload': return Math.min((15 - numericValue) / 15 * 100, 100); // Обратная логика - меньше лучше
    case 'rof': return Math.min(numericValue / 12 * 100, 100);
    case 'aim': return Math.min((3 - numericValue) / 3 * 100, 100); // Обратная логика
    case 'dispersion': return Math.min((0.5 - numericValue) / 0.5 * 100, 100); // Обратная логика

    // Мобильность
    case 'speed': return Math.min(numericValue / 70 * 100, 100);
    case 'reverse': return Math.min(numericValue / 30 * 100, 100);
    case 'engine': return Math.min(numericValue / 1000 * 100, 100);
    case 'power': return Math.min(numericValue / 30 * 100, 100);
    case 'traverse': return Math.min(numericValue / 60 * 100, 100);

    // Прочие характеристики
    case 'hp': return Math.min(numericValue / 2500 * 100, 100);
    case 'view': return Math.min(numericValue / 500 * 100, 100);
    case 'radio': return Math.min(numericValue / 1000 * 100, 100);
    case 'fire': return Math.min((20 - numericValue) / 20 * 100, 100); // Обратная логика
    case 'hull': return Math.min(numericValue / 300 * 100, 100);
    case 'turret': return Math.min(numericValue / 300 * 100, 100);

    default: return Math.min(numericValue / 100 * 100, 100);
  }
}

// Функция для форматирования значений характеристик
function formatCharacteristicValue(value, type) {
  // Если значение "Н/Д", возвращаем как есть
  if (value === 'Н/Д' || value === null || value === undefined) {
    return 'Н/Д';
  }

  const numericValue = parseFloat(value.toString().replace(/[^\d.]/g, ''));
  if (isNaN(numericValue)) return value;

  switch (type) {
    case 'dpm': return numericValue.toLocaleString();
    case 'damage': return Math.round(numericValue).toString();
    case 'penetration': return Math.round(numericValue) + ' мм';
    case 'reload': return numericValue.toFixed(1) + ' с';
    case 'rof': return Math.round(numericValue) + ' выстр/мин';
    case 'aim': return numericValue.toFixed(1) + ' с';
    case 'dispersion': return numericValue.toFixed(2) + ' м';
    case 'speed': return Math.round(numericValue) + ' км/ч';
    case 'reverse': return Math.round(numericValue) + ' км/ч';
    case 'engine': return Math.round(numericValue) + ' л.с.';
    case 'power': return numericValue.toFixed(1) + ' л.с./т';
    case 'traverse': return numericValue.toFixed(1) + ' °/с';
    case 'hp': return Math.round(numericValue).toLocaleString();
    case 'view': return Math.round(numericValue) + ' м';
    case 'radio': return numericValue.toFixed(1) + ' м';
    case 'fire': return Math.round(numericValue) + ' %';
    case 'hull': return Math.round(numericValue) + ' мм';
    case 'turret': return Math.round(numericValue) + ' мм';
    default: return value;
  }
}

// Скрыть детали танка
function hideTankDetails() {
  // Очищаем все обработчики скролла при скрытии деталей танка
  if (typeof clearScrollTimeouts === 'function') {
    clearScrollTimeouts();
  }
  window.removeEventListener('scroll', handleUserScroll);

  const container = document.getElementById('tank-characteristics-container');
  if (container) {
    container.classList.add('hidden');
    container.style.display = 'none';
    container.style.opacity = '0';
    container.style.visibility = 'hidden';
  }

  // Показываем список танков
  const tankListElement = document.getElementById('tank-list');
  if (tankListElement) {
    tankListElement.classList.remove('hidden');
    tankListElement.style.display = 'grid';
    tankListElement.style.opacity = '1';
    tankListElement.style.visibility = 'visible';
  }
}

// Показать детали танка
function showTankDetails() {
  const container = document.getElementById('tank-characteristics-container');
  if (container) {
    container.classList.remove('hidden');
    container.style.display = 'block';
    container.style.opacity = '1';
    container.style.visibility = 'visible';
  }

  // Сбрасываем прогресс-бары
  const progressBars = document.querySelectorAll('[id$="Fill"]');
  progressBars.forEach(bar => {
    bar.style.width = '0%';
    bar.style.display = 'none';
  });
}

// Обновление заголовка танка
function updateTankHeader(tankObject) {
  const elements = {
    icon: document.querySelector('#tank-details-icon'),
    flag: document.querySelector('#tank-country-flag'),
    name: document.querySelector('.tank-name-large'),
    roleText: document.querySelector('.tank-role .role-text'),
    roleIcon: document.querySelector('.tank-role .role-icon-small'),
    price: document.querySelector('.tank-price .price-value'),
    currencyIcon: document.querySelector('.tank-price .currency-icon-small')
  };

  // Обновляем иконку танка
  if (elements.icon) {
    const iconPath = tankObject.icon || 'src/assets/images/tanks/default.webp';
    elements.icon.src = iconPath;
    elements.icon.alt = tankObject.name || 'Иконка танка';
  }

  // Обновляем флаг страны
  if (elements.flag) {
    elements.flag.src = getBlurredFlagPath(tankObject.country);
    elements.flag.alt = tankObject.country || 'Страна';
  }

  // Обновляем название
  if (elements.name) {
    elements.name.textContent = tankObject.name || 'Название танка';
  }

  // Обновляем роль
  if (elements.roleText && elements.roleIcon) {
    const roleObject = tankObject.role;

    if (roleObject && roleObject.name && roleObject.icon) {
      elements.roleText.textContent = roleObject.name;
      elements.roleIcon.src = roleObject.icon;
      elements.roleIcon.alt = roleObject.name;
    } else {
      const fallbackRole = tankObject.role || 'Роль';
      elements.roleText.textContent = fallbackRole;
      elements.roleIcon.src = 'src/assets/images/role/unknown.png';
      elements.roleIcon.alt = fallbackRole;
    }
  }

  // Обновляем цену - скрываем если нет цены
  if (elements.price) {
    if (tankObject.cost) {
      elements.price.textContent = tankObject.cost.toLocaleString();
      elements.price.parentElement.style.display = 'flex';
    } else {
      elements.price.parentElement.style.display = 'none';
    }
  }

  // Обновляем иконку валюты
  if (elements.currencyIcon && tankObject.cost) {
    if (tankObject.currencyIcon) {
      elements.currencyIcon.src = tankObject.currencyIcon;
      elements.currencyIcon.alt = "Бонусы";
    } else {
      elements.currencyIcon.src = "src/assets/images/role/silver.png";
      elements.currencyIcon.alt = "Серебро";
    }
  }

  // Обновляем изображение слота оборудования
  const equipmentSlot1 = document.getElementById('equipment-slot-1');
  if (equipmentSlot1 && tankObject.equipmentSlot1Image) {
    equipmentSlot1.style.backgroundImage = `url('${tankObject.equipmentSlot1Image}')`;
  }
}

// Инициализация обработчиков вкладок
function initializeTabHandlers() {
  const tabs = document.querySelectorAll('.tab-button');
  const tabContents = document.querySelectorAll('.tab-content');

  tabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const targetTab = tab.dataset.tab;

      // Обновляем активную вкладку
      tabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');

      // Показываем соответствующий контент
      tabContents.forEach(content => {
        if (content.id === `${targetTab}-tab`) {
          content.classList.add('active');
          content.style.display = 'block';
        } else {
          content.classList.remove('active');
          content.style.display = 'none';
        }
      });
    });
  });
}

// Инициализация подсказок
function initializeTooltips() {
  // Создаем глобальный элемент подсказки
  let globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) {
    globalTooltip = document.createElement('div');
    globalTooltip.id = 'global-tooltip';
    globalTooltip.className = 'global-tooltip';
    document.body.appendChild(globalTooltip);
  }
}

// Настройка подсказок
function setupTooltips() {
  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  // Применяем стили в зависимости от темы
  applyTooltipTheme();

  // Удаляем старые обработчики
  const tooltipElements = document.querySelectorAll('.characteristic-tooltip');
  tooltipElements.forEach(element => {
    element.removeEventListener('mouseenter', handleTooltipMouseEnter);
    element.removeEventListener('mouseleave', handleTooltipMouseLeave);
    element.removeEventListener('mousemove', handleTooltipMouseMove);
  });

  // Добавляем новые обработчики
  tooltipElements.forEach(element => {
    element.addEventListener('mouseenter', handleTooltipMouseEnter);
    element.addEventListener('mouseleave', handleTooltipMouseLeave);
    element.addEventListener('mousemove', handleTooltipMouseMove);
  });
}

// Применение темы к подсказке
function applyTooltipTheme() {
  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  const isDarkTheme = document.documentElement.getAttribute('data-theme') === 'dark';

  if (isDarkTheme) {
    globalTooltip.style.backgroundColor = '#2d3748';
    globalTooltip.style.color = '#f7fafc';
    globalTooltip.style.border = '1px solid #4a5568';
  } else {
    globalTooltip.style.backgroundColor = '#f8fafc';
    globalTooltip.style.color = '#111827';
    globalTooltip.style.border = '1px solid #e6e6e6';
  }
}

// Обработчики событий подсказок
function handleTooltipMouseEnter(e) {
  const tooltipText = this.querySelector('.tooltip-text');
  if (!tooltipText) return;

  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  globalTooltip.innerHTML = tooltipText.innerHTML;
  positionTooltip(e);
  globalTooltip.style.display = 'block';
}

function handleTooltipMouseLeave() {
  const globalTooltip = document.getElementById('global-tooltip');
  if (globalTooltip) {
    globalTooltip.style.display = 'none';
  }
}

function handleTooltipMouseMove(e) {
  positionTooltip(e);
}

// Позиционирование подсказки
function positionTooltip(e) {
  const globalTooltip = document.getElementById('global-tooltip');
  if (!globalTooltip) return;

  const offsetX = 25;
  const offsetY = 10;

  let left = e.clientX + offsetX;
  let top = e.clientY - offsetY;

  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  const tooltipWidth = globalTooltip.offsetWidth;
  const tooltipHeight = globalTooltip.offsetHeight;

  // Проверка границ
  if (left + tooltipWidth > viewportWidth - 10) {
    left = e.clientX - tooltipWidth - offsetX;
  }

  if (top + tooltipHeight > viewportHeight - 10) {
    top = e.clientY - tooltipHeight - offsetY;
  }

  globalTooltip.style.left = `${left}px`;
  globalTooltip.style.top = `${top}px`;
}

// Наблюдатель за изменением темы
const themeObserver = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.attributeName === 'data-theme') {
      applyTooltipTheme();
    }
  });
});

// Запускаем наблюдатель
themeObserver.observe(document.documentElement, { attributes: true });

// Переменные для хранения состояния сортировки
let currentSortColumn = null;
let currentSortDirection = 'desc'; // По умолчанию сортировка по убыванию
let currentTankBuilds = null; // Сохраняем текущие сборки для пересортировки

// Функция сортировки таблицы сборок
window.sortBuildsTable = function(column, headerElement) {
  // Сохраняем текущие данные сборок если их еще нет
  if (!currentTankBuilds) {
    const tankNameElement = document.querySelector('.tank-name');
    if (tankNameElement) {
      const tankName = tankNameElement.textContent;
      // Получаем танк из глобального массива
      const tank = window.tanksData?.find(t => t.name === tankName);
      if (tank && tank.bestBuilds) {
        currentTankBuilds = tank;
      }
    }
  }

  if (!currentTankBuilds || !currentTankBuilds.bestBuilds) return;

  // Определяем направление сортировки
  if (currentSortColumn === column) {
    currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
  } else {
    currentSortDirection = 'desc';
    currentSortColumn = column;
  }

  // Копируем массив сборок для сортировки
  const sortedBuilds = [...currentTankBuilds.bestBuilds];

  // Сортируем в зависимости от колонки
  sortedBuilds.sort((a, b) => {
    let aValue, bValue;

    switch(column) {
      case 'efficiency':
        aValue = parseFloat(a.efficiency) || 0;
        bValue = parseFloat(b.efficiency) || 0;
        break;
      case 'contribution':
        aValue = parseFloat(a.contribution) || 0;
        bValue = parseFloat(b.contribution) || 0;
        break;
      case 'damage':
        aValue = parseFloat(a.damage) || 0;
        bValue = parseFloat(b.damage) || 0;
        break;
      case 'assist':
        aValue = parseFloat(a.assist) || 0;
        bValue = parseFloat(b.assist) || 0;
        break;
      case 'dpm':
        aValue = parseFloat(a.dpm) || 0;
        bValue = parseFloat(b.dpm) || 0;
        break;
      case 'spread':
        aValue = parseFloat(a.spread) || 0;
        bValue = parseFloat(b.spread) || 0;
        break;
      case 'dynSpread':
        aValue = parseFloat(a.dynSpread) || 0;
        bValue = parseFloat(b.dynSpread) || 0;
        break;
      case 'aimTime':
        aValue = parseFloat(a.aimTime) || 0;
        bValue = parseFloat(b.aimTime) || 0;
        break;
      case 'viewRange':
        aValue = parseFloat(a.viewRange) || 0;
        bValue = parseFloat(b.viewRange) || 0;
        break;
      case 'dispersion':
        aValue = parseFloat(a.dispersion) || 0;
        bValue = parseFloat(b.dispersion) || 0;
        break;
      case 'cv':
        aValue = parseFloat(a.cv) || 0;
        bValue = parseFloat(b.cv) || 0;
        break;
      case 'accuracy':
        aValue = parseFloat(a.accuracy) || 0;
        bValue = parseFloat(b.accuracy) || 0;
        break;
      default:
        return 0;
    }

    // Для некоторых колонок меньшее значение лучше
    const inverseSortColumns = ['spread', 'dynSpread', 'aimTime', 'dispersion', 'cv'];
    const isInverseSort = inverseSortColumns.includes(column);

    if (currentSortDirection === 'asc') {
      return isInverseSort ? bValue - aValue : aValue - bValue;
    } else {
      return isInverseSort ? aValue - bValue : bValue - aValue;
    }
  });

  // Обновляем визуальные индикаторы сортировки
  updateSortIndicators(headerElement);

  // Перерисовываем таблицу с отсортированными данными
  const tempTank = { ...currentTankBuilds, bestBuilds: sortedBuilds };
  renderBuildsTable(tempTank);
};

// Функция обновления индикаторов сортировки
function updateSortIndicators(activeHeader) {
  // Сбрасываем все индикаторы
  document.querySelectorAll('.builds-table-daisy th.sortable').forEach(th => {
    const arrow = th.querySelector('.sort-arrow');
    if (arrow) {
      arrow.textContent = '';
      arrow.style.opacity = '0';
    }
    th.classList.remove('sorted-asc', 'sorted-desc');
  });

  // Устанавливаем активный индикатор
  if (activeHeader) {
    const arrow = activeHeader.querySelector('.sort-arrow');
    if (arrow) {
      arrow.textContent = currentSortDirection === 'asc' ? '↑' : '↓';
      arrow.style.opacity = '1';
    }
    activeHeader.classList.add(`sorted-${currentSortDirection}`);
  }
}

// Переменная для предотвращения множественных вызовов
let toggleInProgress = false;

// Функция для переключения видимости секции примечаний
window.toggleNotesSection = function() {
  // Предотвращаем множественные вызовы во время анимации
  if (toggleInProgress) {
    console.log('⏳ ПЕРЕКЛЮЧЕНИЕ УЖЕ В ПРОЦЕССЕ, ИГНОРИРУЕМ');
    return;
  }

  toggleInProgress = true;
  const notesSection = document.querySelector('.notes-section');
  const toggleIcon = document.getElementById('toggle-notes-icon');

  if (!notesSection) {
    console.warn('Notes section not found');
    return;
  }

  // Проверяем текущее состояние через класс
  const isHidden = notesSection.classList.contains('notes-hidden');

  // Очищаем предыдущие таймауты
  clearScrollTimeouts();

  if (isHidden) {
    // ========== ПОКАЗЫВАЕМ ПРИМЕЧАНИЯ ==========
    console.log('🔄 ПОКАЗЫВАЕМ ПРИМЕЧАНИЯ');

    // Показываем примечания
    notesSection.classList.remove('notes-hidden');

    // Обновляем иконку
    if (toggleIcon) {
      toggleIcon.style.transform = 'rotate(0deg)';
      toggleIcon.style.stroke = '#ff3b30';
    }

    // Настраиваем отслеживание ручного скролла
    userScrolledManually = false;
    lastScrollPosition = window.pageYOffset;
    setupScrollTracking();

    // Просто ждем завершения CSS анимации без автоматического скролла
    const scrollTimeout = setTimeout(() => {
      console.log('✅ ПРИМЕЧАНИЯ ОТКРЫТЫ БЕЗ АВТОСКРОЛЛА');

      // Принудительно обновляем layout
      notesSection.offsetHeight;

      // Сбрасываем флаги
      programmaticScrollInProgress = false;
      lastScrollPosition = window.pageYOffset;

    }, 350); // Ждем завершения CSS анимации

    scrollTimeouts.push(scrollTimeout);

  } else {
    // ========== СКРЫВАЕМ ПРИМЕЧАНИЯ ==========
    console.log('🔄 СКРЫВАЕМ ПРИМЕЧАНИЯ');

    // Сохраняем данные ПЕРЕД скрытием
    const currentScrollTop = window.pageYOffset;
    const notesRect = notesSection.getBoundingClientRect();
    const notesTopInDocument = currentScrollTop + notesRect.top;
    const notesHeight = notesRect.height;

    console.log('📏 ДАННЫЕ ПЕРЕД СКРЫТИЕМ:', {
      currentScrollTop,
      notesTopInDocument,
      notesHeight
    });

    // Сбрасываем флаги
    userScrolledManually = false;
    programmaticScrollInProgress = false;

    // Удаляем обработчик скролла
    window.removeEventListener('scroll', handleUserScroll);

    // Плавно скрываем примечания
    notesSection.classList.add('notes-hidden');

    // Обновляем иконку
    if (toggleIcon) {
      toggleIcon.style.transform = 'rotate(180deg)';
      toggleIcon.style.stroke = '#FFA726';
    }

    // Плавная коррекция скролла ТОЛЬКО если пользователь был ниже примечаний
    const hideTimeout = setTimeout(() => {
      // Если пользователь был прокручен ниже начала примечаний
      if (currentScrollTop > notesTopInDocument) {
        // Плавно корректируем позицию вверх на высоту примечаний
        const newScrollTop = Math.max(0, currentScrollTop - notesHeight);

        console.log('🔧 ПЛАВНАЯ КОРРЕКЦИЯ СКРОЛЛА:', {
          oldScrollTop: currentScrollTop,
          newScrollTop,
          notesHeight
        });

        // Используем нашу плавную функцию
        smoothScrollCorrection(newScrollTop, 300);
      } else {
        console.log('✅ КОРРЕКЦИЯ НЕ НУЖНА - пользователь выше примечаний');
      }

    }, 280); // Ждем завершения анимации скрытия

    scrollTimeouts.push(hideTimeout);

    console.log('📝 ПРИМЕЧАНИЯ ПЛАВНО СКРЫТЫ');
  }

  // Сбрасываем флаг через время, достаточное для завершения всех анимаций
  setTimeout(() => {
    toggleInProgress = false;
  }, 400); // Время анимации + буфер
};

// Экспорт функций
export {
  updateTankHeader,
  showTankDetails,
  hideTankDetails,
  setupTooltips
};
