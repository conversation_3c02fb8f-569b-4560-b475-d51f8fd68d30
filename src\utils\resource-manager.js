/**
 * Resource Manager - Управление ресурсами приложения
 * Оптимизированная система управления памятью и ресурсами
 */

// Менеджер ресурсов
class ResourceManager {
  constructor() {
    this.resources = new Map();
    this.observers = new Set();
    this.cleanupTasks = new Set();
    this.memoryThreshold = 50 * 1024 * 1024; // 50MB
    this.isCleanupRunning = false;
  }

  /**
   * Регистрация ресурса
   * @param {string} id - Идентификатор ресурса
   * @param {*} resource - Ресурс
   * @param {Function} cleanup - Функция очистки
   */
  register(id, resource, cleanup = null) {
    this.resources.set(id, {
      resource,
      cleanup,
      timestamp: Date.now(),
      accessCount: 0
    });
  }

  /**
   * Получение ресурса
   * @param {string} id - Идентификатор ресурса
   * @returns {*} Ресурс
   */
  get(id) {
    const entry = this.resources.get(id);
    if (entry) {
      entry.accessCount++;
      entry.lastAccess = Date.now();
      return entry.resource;
    }
    return null;
  }

  /**
   * Освобождение ресурса
   * @param {string} id - Идентификатор ресурса
   */
  release(id) {
    const entry = this.resources.get(id);
    if (entry) {
      if (entry.cleanup && typeof entry.cleanup === 'function') {
        try {
          entry.cleanup(entry.resource);
        } catch (error) {
          console.warn(`Cleanup failed for resource ${id}:`, error);
        }
      }
      this.resources.delete(id);
    }
  }

  /**
   * Автоматическая очистка неиспользуемых ресурсов
   * @param {number} maxAge - Максимальный возраст ресурса (мс)
   */
  cleanup(maxAge = 5 * 60 * 1000) { // 5 минут по умолчанию
    if (this.isCleanupRunning) return;
    
    this.isCleanupRunning = true;
    const now = Date.now();
    const toDelete = [];

    for (const [id, entry] of this.resources) {
      const age = now - (entry.lastAccess || entry.timestamp);
      if (age > maxAge && entry.accessCount === 0) {
        toDelete.push(id);
      }
    }

    toDelete.forEach(id => this.release(id));
    this.isCleanupRunning = false;

    if (toDelete.length > 0) {
      console.log(`🧹 Cleaned up ${toDelete.length} unused resources`);
    }
  }

  /**
   * Принудительная очистка всех ресурсов
   */
  clear() {
    for (const id of this.resources.keys()) {
      this.release(id);
    }
    this.resources.clear();
  }

  /**
   * Получение статистики ресурсов
   * @returns {Object} Статистика
   */
  getStats() {
    const stats = {
      totalResources: this.resources.size,
      memoryUsage: 0,
      oldestResource: null,
      mostAccessed: null
    };

    let oldestTime = Date.now();
    let maxAccess = 0;

    for (const [id, entry] of this.resources) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        stats.oldestResource = id;
      }
      if (entry.accessCount > maxAccess) {
        maxAccess = entry.accessCount;
        stats.mostAccessed = id;
      }
    }

    // Приблизительная оценка использования памяти
    if (performance.memory) {
      stats.memoryUsage = performance.memory.usedJSHeapSize;
    }

    return stats;
  }

  /**
   * Мониторинг памяти
   */
  startMemoryMonitoring() {
    const checkMemory = () => {
      if (performance.memory) {
        const used = performance.memory.usedJSHeapSize;
        if (used > this.memoryThreshold) {
          console.warn(`🚨 Memory usage high: ${Math.round(used / 1024 / 1024)}MB`);
          this.cleanup(2 * 60 * 1000); // Более агрессивная очистка
        }
      }
    };

    setInterval(checkMemory, 30000); // Проверка каждые 30 секунд
  }
}

// Глобальный экземпляр менеджера ресурсов
export const resourceManager = new ResourceManager();

/**
 * Менеджер изображений
 */
export class ImageManager {
  constructor() {
    this.cache = new Map();
    this.loading = new Set();
    this.maxCacheSize = 100;
  }

  /**
   * Загрузка изображения с кэшированием
   * @param {string} src - URL изображения
   * @returns {Promise<HTMLImageElement>} Промис с изображением
   */
  async load(src) {
    // Проверяем кэш
    if (this.cache.has(src)) {
      return this.cache.get(src);
    }

    // Проверяем, не загружается ли уже
    if (this.loading.has(src)) {
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (this.cache.has(src)) {
            resolve(this.cache.get(src));
          } else {
            setTimeout(checkLoaded, 10);
          }
        };
        checkLoaded();
      });
    }

    this.loading.add(src);

    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        this.loading.delete(src);
        
        // Управление размером кэша
        if (this.cache.size >= this.maxCacheSize) {
          const firstKey = this.cache.keys().next().value;
          this.cache.delete(firstKey);
        }
        
        this.cache.set(src, img);
        resolve(img);
      };

      img.onerror = () => {
        this.loading.delete(src);
        reject(new Error(`Failed to load image: ${src}`));
      };

      img.src = src;
    });
  }

  /**
   * Предзагрузка изображений
   * @param {string[]} urls - Массив URL изображений
   */
  preload(urls) {
    urls.forEach(url => {
      if (!this.cache.has(url) && !this.loading.has(url)) {
        this.load(url).catch(() => {
          // Игнорируем ошибки предзагрузки
        });
      }
    });
  }

  /**
   * Очистка кэша изображений
   */
  clear() {
    this.cache.clear();
    this.loading.clear();
  }
}

// Глобальный экземпляр менеджера изображений
export const imageManager = new ImageManager();

/**
 * Менеджер событий с автоочисткой
 */
export class EventManager {
  constructor() {
    this.listeners = new Map();
  }

  /**
   * Добавление слушателя события
   * @param {Element} element - Элемент
   * @param {string} event - Тип события
   * @param {Function} handler - Обработчик
   * @param {Object} options - Опции
   * @returns {Function} Функция для удаления слушателя
   */
  add(element, event, handler, options = {}) {
    const optimizedOptions = {
      passive: true,
      ...options
    };

    element.addEventListener(event, handler, optimizedOptions);

    const key = `${element.tagName}-${event}-${Date.now()}`;
    this.listeners.set(key, {
      element,
      event,
      handler,
      options: optimizedOptions
    });

    return () => this.remove(key);
  }

  /**
   * Удаление слушателя
   * @param {string} key - Ключ слушателя
   */
  remove(key) {
    const listener = this.listeners.get(key);
    if (listener) {
      listener.element.removeEventListener(
        listener.event,
        listener.handler,
        listener.options
      );
      this.listeners.delete(key);
    }
  }

  /**
   * Очистка всех слушателей
   */
  clear() {
    for (const key of this.listeners.keys()) {
      this.remove(key);
    }
  }

  /**
   * Получение статистики слушателей
   * @returns {Object} Статистика
   */
  getStats() {
    return {
      totalListeners: this.listeners.size,
      eventTypes: [...new Set(Array.from(this.listeners.values()).map(l => l.event))]
    };
  }
}

// Глобальный экземпляр менеджера событий
export const eventManager = new EventManager();

/**
 * Инициализация менеджера ресурсов
 */
export function initResourceManager() {
  console.log('🗂️ Resource manager initialized');

  // Запускаем мониторинг памяти
  resourceManager.startMemoryMonitoring();

  // Автоматическая очистка каждые 5 минут
  setInterval(() => {
    resourceManager.cleanup();
  }, 5 * 60 * 1000);

  // Очистка при выгрузке страницы
  if (typeof window !== 'undefined') {
    window.addEventListener('beforeunload', () => {
      resourceManager.clear();
      imageManager.clear();
      eventManager.clear();
    });

    // Очистка при скрытии страницы (мобильные устройства)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        resourceManager.cleanup(2 * 60 * 1000); // Более агрессивная очистка
      }
    });
  }

  // Логирование статистики в dev режиме
  if (import.meta.env.DEV) {
    setInterval(() => {
      const stats = resourceManager.getStats();
      const eventStats = eventManager.getStats();
      console.log('📊 Resource stats:', {
        resources: stats.totalResources,
        events: eventStats.totalListeners,
        memory: stats.memoryUsage ? `${Math.round(stats.memoryUsage / 1024 / 1024)}MB` : 'N/A'
      });
    }, 60000); // Каждую минуту
  }
}

// Экспорт для глобального использования
if (typeof window !== 'undefined') {
  window.resourceManager = resourceManager;
  window.imageManager = imageManager;
  window.eventManager = eventManager;
}
