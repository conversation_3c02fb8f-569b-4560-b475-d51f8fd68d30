// Импорт стилей
import './assets/styles/tailwind.css';

// Импорт данных
import { tanksData } from './data/tanks.js';

// Импорт утилит
import { initPerformanceOptimizations, performanceMonitor, getCachedElement, getDebounced } from './utils/performance.js';
import { initLazyLoader, lazyLoad } from './utils/lazy-loader.js';
import { initResourceManager } from './utils/resource-manager.js';
import { RUSSIAN_TO_INTERNAL_TYPE_MAP, ENGLISH_TO_INTERNAL_TYPE_MAP, getFilteredTanks as getFilteredTanksModule } from './services/FilterService.js';
import { initRouter } from './router/index.js';
import { TankSearchIndex } from './services/SearchService.js';
import { state } from './store/state.js';
import { setupSidebarMenuItems, hideAllSections, showSection } from './utils/ui.js';
import { initTheme } from './utils/theme.js';

// Импорт компонентов UI
import { renderTankList, updateFilterSelection } from './components/tank-list/index.js';

// Local copies used during ongoing refactor
let searchIndex = null;

// Флаг для предотвращения множественного рендеринга при инициализации
let isInitialRenderComplete = false;

// Unified filtering helper that delegates to the dedicated module
function getFilteredTanks() {
    return getFilteredTanksModule(state, searchIndex);
}

import { getTankTypeClass, getFlagPath } from './utils/constants.js';
import { createTankId } from './utils/helpers.js';

// Build flattened array of all tanks and cache it in state
function initializeAllTanksCache() {
    if (state.allTanks && state.allTanks.length) return;

    const flattened = [];
    const processedTanks = new Set(); // Для отслеживания уже добавленных танков

    Object.entries(tanksData).forEach(([country, types]) => {
        Object.entries(types).forEach(([type, tanks]) => {
            tanks.forEach(tank => {
                // Создаем уникальный ключ для танка
                const tankKey = `${tank.name}_${country}_${type}`;
                
                // Проверяем, не был ли этот танк уже добавлен
                if (!processedTanks.has(tankKey)) {
                    flattened.push({ ...tank, country, type });
                    processedTanks.add(tankKey);
                }
            });
        });
    });

    state.allTanks = flattened;
    console.log(`[Init] Loaded ${flattened.length} unique tanks`);
}

// Оптимизированная инициализация данных танков с батчингом
async function initializeAllTanksCacheOptimized() {
    if (state.allTanks && state.allTanks.length) return;

    const BATCH_SIZE = 50;
    const flattened = [];
    const processedTanks = new Set(); // Для предотвращения дубликатов
    let processed = 0;
    let total = 0;

    // Подсчитываем общее количество танков
    Object.values(tanksData).forEach(types => {
        Object.values(types).forEach(tanks => {
            total += tanks.length;
        });
    });

    console.log(`[Init] Processing ${total} tanks in batches of ${BATCH_SIZE}`);

    for (const [country, types] of Object.entries(tanksData)) {
        for (const [type, tanks] of Object.entries(types)) {
            for (let i = 0; i < tanks.length; i += BATCH_SIZE) {
                const batch = tanks.slice(i, i + BATCH_SIZE);

                batch.forEach(tank => {
                    const tankKey = `${tank.name}_${country}_${type}`;
                    
                    if (!processedTanks.has(tankKey)) {
                        flattened.push({ ...tank, country, type });
                        processedTanks.add(tankKey);
                        processed++;
                    }
                });

                updateLoadingProgress(processed, total);

                if (i + BATCH_SIZE < tanks.length) {
                    await new Promise(resolve => setTimeout(resolve, 0));
                }
            }
        }
    }

    state.allTanks = flattened;
    console.log(`[Init] ✅ Loaded ${flattened.length} unique tanks`);
}

// Асинхронное построение поискового индекса
async function buildSearchIndexAsync() {
    return new Promise((resolve) => {
        if (typeof requestIdleCallback !== 'undefined') {
            requestIdleCallback(() => {
                console.log('[Init] Building search index...');
                searchIndex = new TankSearchIndex(state.allTanks);
                console.log('[Init] ✅ Search index built');
                resolve();
            });
        } else {
            // Fallback для браузеров без requestIdleCallback
            setTimeout(() => {
                console.log('[Init] Building search index...');
                searchIndex = new TankSearchIndex(state.allTanks);
                console.log('[Init] ✅ Search index built');
                resolve();
            }, 0);
        }
    });
}

// Removed unused function: saveAppStateBeforeReload

// Функция восстановления состояния после F5
function restoreAppStateAfterReload() {
    console.log('[Init] Restoring app state after reload...');

    // Восстанавливаем сохраненное состояние
    const savedState = localStorage.getItem('appState');
    if (savedState) {
        try {
            const parsedState = JSON.parse(savedState);
            console.log('[Init] Found saved state:', parsedState);

            // Проверяем, что состояние не слишком старое (не более 5 минут)
            if (Date.now() - parsedState.timestamp < 5 * 60 * 1000) {
                if (parsedState.activeTab === 'vehicles' && parsedState.vehiclesVisible) {
                    console.log('[Init] Restoring vehicles tab state');
                    // Не вызываем showSection здесь, так как он уже вызван в setupSidebarMenuItems
                }
            }
        } catch (error) {
            console.warn('[Init] Error parsing saved state:', error);
        }
    }

    // Выполняем финальный рендеринг только один раз
    setTimeout(() => {
        const vehiclesTab = document.querySelector('[data-section="vehicles"]');
        const isVehiclesActive = vehiclesTab && vehiclesTab.classList.contains('active');

        if (isVehiclesActive) {
            console.log('[Init] Performing final tank list render');
            isInitialRenderComplete = true;
            window.isInitialRenderComplete = true;
            applyFiltersAndRenderTankList(false, true);
        } else {
            isInitialRenderComplete = true;
            window.isInitialRenderComplete = true;
        }
    }, 200);

    // Проверяем hash для характеристик танка
    const hash = window.location.hash;
    if (hash && hash.startsWith('#')) {
        const tankName = decodeURIComponent(hash.substring(1));
        const tankId = createTankId(tankName);
        console.log(`[Init] Found tank hash: ${tankName}, showing characteristics`);

        setTimeout(() => {
            showTankCharacteristics(tankId);
        }, 200);
    }
}

// Инициализация UI компонентов с requestAnimationFrame
async function initializeUIComponents() {
    return new Promise((resolve) => {
        requestAnimationFrame(() => {
            setupSidebarMenuItems();
            // Восстанавливаем состояние после инициализации
            restoreAppStateAfterReload();
            resolve();
        });
    });
}

// Инициализация роутинга и событий
async function initializeRouterAndEvents() {
    return new Promise((resolve) => {
        requestAnimationFrame(() => {
            setupEventDelegation();
            initRouter({ initializeAllTanksCache, cacheDOMElements, handleTankSelection, applyFiltersAndRenderTankList });
            initTankCharacteristicsRouter();
            resolve();
        });
    });
}

// Дополнительные компоненты (lazy loading)
function initializeAdditionalComponents() {
    try {
        initializeCustomDropdowns();
        initializeCompareSystem();
        initializeSettingsHandlers();

        setTimeout(diagnoseApp, 1000);

        console.log('[Init] ✅ Additional components initialized');
    } catch (error) {
        console.warn('[Init] ⚠️ Error in additional components:', error);
    }
}

// Функции для индикатора загрузки
function showLoadingIndicator() {
    const loadingHTML = `
        <div id="app-loading-overlay" class="loading-overlay">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <div class="loading-text">Загрузка танков...</div>
                <div class="loading-progress">
                    <div class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                    <div id="progress-text" class="progress-text">0%</div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', loadingHTML);
}

function hideLoadingIndicator() {
    const overlay = document.getElementById('app-loading-overlay');
    if (overlay) {
        overlay.style.opacity = '0';
        setTimeout(() => overlay.remove(), 300);
    }
}

function updateLoadingProgress(current, total) {
    const percentage = Math.round((current / total) * 100);
    const progressFill = document.getElementById('progress-fill');
    const progressText = document.getElementById('progress-text');

    if (progressFill) progressFill.style.width = `${percentage}%`;
    if (progressText) progressText.textContent = `${percentage}%`;
}

function showErrorMessage(message) {
    const errorHTML = `
        <div id="app-error-overlay" class="error-overlay">
            <div class="error-container">
                <div class="error-icon">⚠️</div>
                <div class="error-text">${message}</div>
                <button onclick="location.reload()" class="error-button">Обновить страницу</button>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', errorHTML);
}

// Removed unused function: resetAllFilters

// Глобальная функция для принудительного отображения списка танков
window.forceShowTankList = function() {
    console.log("Forcing tank list display");

    if (window.appState) {
        window.appState.selectedTank = null;
    }

    const tankListElement = document.getElementById('tank-list');
    if (tankListElement) {
        tankListElement.classList.remove('hidden');
        tankListElement.style.display = 'grid';
        tankListElement.style.opacity = '1';
        tankListElement.style.visibility = 'visible';

        const filteredTanks = window.getFilteredTanks ? window.getFilteredTanks() : [];
        if (window.renderTankList) {
            window.renderTankList(filteredTanks, null, null);
        }
    }

    const charContainer = document.getElementById('tank-characteristics-container');
    if (charContainer) {
        charContainer.classList.add('hidden');
        charContainer.style.display = 'none';
        charContainer.style.opacity = '0';
        charContainer.style.visibility = 'hidden';
    }

    const backBtn = document.getElementById('back-icon');
    if (backBtn) {
        backBtn.classList.add('hidden');
    }

    if (window.updateTankList) {
        window.updateTankList();
    }

    return "Tank list display forced";
};

// Функция для преобразования названий стран из внутреннего формата в формат HTML
function getCountryForUI(internalCountry) {
    const countryMap = {
        'USSR': 'ussr',
        'Germany': 'germany',
        'USA': 'usa',
        'France': 'france',
        'UK': 'uk',
        'Czech': 'czech',
        'China': 'china',
        'Japan': 'japan',
        'Poland': 'poland',
        'Sweden': 'sweden',
        'Italy': 'italy',
        'International': 'international'
    };
    return countryMap[internalCountry] || internalCountry.toLowerCase();
}



// Экспортируем функции и состояние в глобальную область для отладки
window.appState = { selectedTank: null };
window.getFilteredTanks = getFilteredTanks;
window.updateTankList = updateTankList;

// Функция для отображения страницы характеристик танка
function showTankCharacteristics(tankId) {
    const tank = state.allTanks.find(t => createTankId(t.name) === tankId);
    if (!tank) {
        console.error(`Tank with ID ${tankId} not found`);
        return;
    }

    state.selectedTank = tank;
    window.currentTankId = tankId;

    hideAllSections();

    const characteristicsContainer = document.getElementById('tank-characteristics-container');
    if (!characteristicsContainer) {
        console.error('Tank characteristics container not found!');
        return;
    }

    characteristicsContainer.classList.add('hidden');
    characteristicsContainer.style.display = 'none';

    // Используем оптимизированную ленивую загрузку
    lazyLoad('./components/tank-details/TankDetails.js', 'updateTankDetailsUI')
        .then(updateTankDetailsUI => {
            updateTankDetailsUI(tank);

            setTimeout(() => {
                characteristicsContainer.classList.remove('hidden');
                characteristicsContainer.style.display = 'block';
                characteristicsContainer.style.opacity = '1';
                characteristicsContainer.style.visibility = 'visible';
            }, 100);
        })
        .catch(error => {
            console.error('Error loading TankDetails module:', error);
        });
}

// Инициализация роутера для характеристик танков
function initTankCharacteristicsRouter() {
    const hash = window.location.hash;

    if (hash && hash.startsWith('#')) {
        const tankName = decodeURIComponent(hash.substring(1));
        const tankId = createTankId(tankName);

        setTimeout(() => {
            if (state.allTanks && state.allTanks.length > 0) {
                showTankCharacteristics(tankId);
            }
        }, 500);
    }

    window.addEventListener('hashchange', () => {
        const newHash = window.location.hash;
        if (newHash && newHash.startsWith('#')) {
            const tankName = decodeURIComponent(newHash.substring(1));
            const tankId = createTankId(tankName);
            showTankCharacteristics(tankId);
        } else {
            hideAllSections();
            showSection('vehicles');
        }
    });
}

// --- DOM Element References ---
let tankListElement = null;
let searchInput = null;
let countryFilters = null;
let categoryFilters = null;
let tankList = null;
let tankError = null;
let tankCharacteristicsContainer = null;
let notesSection = null;
let modal = null;
let modalTitle = null;
let modalBody = null;
let closeModalBtn = null;
let columnRightElement = null;
let notesToggleButton = null;

// Оптимизированное кэширование DOM элементов
function cacheDOMElements() {
    performanceMonitor.startMeasure('DOM_CACHING');
    console.log("🔄 Caching DOM elements...");

    // Используем оптимизированное кэширование
    tankListElement = getCachedElement('#tank-list');
    searchInput = getCachedElement('#tank-search');
    countryFilters = document.querySelectorAll('#nation-filter .filter-item');
    categoryFilters = document.querySelectorAll('#type-filter .filter-item');
    tankList = tankListElement; // Избегаем дублирования
    tankError = getCachedElement('#tank-error');
    tankCharacteristicsContainer = getCachedElement('#tank-characteristics-container');
    notesSection = getCachedElement('#notes-section');
    modal = getCachedElement('#build-modal');
    modalTitle = getCachedElement('#modal-title');
    modalBody = getCachedElement('#modal-body');
    closeModalBtn = getCachedElement('.close-modal');
    columnRightElement = tankListElement; // Избегаем дублирования
    notesToggleButton = getCachedElement('#toggle-notes-info');

    // Предупреждения только для критических элементов
    if (!searchInput) console.warn("⚠️ 'tank-search' element not found during cache.");
    if (!tankCharacteristicsContainer) console.warn("⚠️ '#tank-characteristics-container' element not found during cache.");

    performanceMonitor.endMeasure('DOM_CACHING');
    console.log("✅ DOM elements cached successfully.");
}

// Update the tank list display
function updateTankList() {
    const filteredTanks = getFilteredTanks();

    if (!state.selectedTank) {
        if (columnRightElement) {
            columnRightElement.classList.remove('hidden');
            columnRightElement.style.display = 'grid';

            renderTankList(filteredTanks, state.allTanks, handleTankSelection, null);
        }

        if (tankCharacteristicsContainer) {
            console.log("updateTankList: Hiding tankCharacteristicsContainer");
            tankCharacteristicsContainer.classList.add('hidden');
        } else { 
            console.error("updateTankList: tankCharacteristicsContainer not found for hiding!"); 
        }

        const backButton = document.getElementById('back-to-list-btn');
        if (backButton) {
            backButton.classList.add('hidden');
        }
    } else {
        console.log("Rendering details for tank:", state.selectedTank.name);

        if (tankCharacteristicsContainer) {
            tankCharacteristicsContainer.classList.remove('hidden');
            tankCharacteristicsContainer.style.display = 'block';
            tankCharacteristicsContainer.style.visibility = 'visible';
            tankCharacteristicsContainer.style.opacity = '1';
        }

        const backButton = document.getElementById('back-to-list-btn');
        if (backButton) {
            backButton.classList.remove('hidden');
        }

        if (tankList) {
            tankList.classList.add('hidden');
        }

        // Загружаем TankDetails модуль динамически
        import('./components/tank-details/TankDetails.js').then(module => {
            module.updateTankDetailsUI(state.selectedTank);
        }).catch(error => {
            console.error('Error loading TankDetails module:', error);
        });
    }

    if (tankError) {
        const shouldShowError = !state.selectedTank && state.searchQuery.length > 0 && filteredTanks.length === 0;
        tankError.classList.toggle('hidden', !shouldShowError);
        if (shouldShowError) tankError.textContent = 'Танк не найден';
    }
}

// --- Event Handlers ---

// Handle country filter selection
function handleCountrySelection(countryValue) {
    if (state.selectedTank) {
        console.log(`Country filter '${countryValue}' clicked. Tank '${state.selectedTank.name}' was selected. Resetting view.`);
        state.selectedTank = null;
        localStorage.removeItem('selectedTank');

        window.location.hash = '';
        history.replaceState({ tankListVisible: true, tankSelected: null }, "", window.location.pathname + window.location.search);

        if (tankCharacteristicsContainer) {
            tankCharacteristicsContainer.classList.add('hidden');
            tankCharacteristicsContainer.style.display = 'none';
        }
        if (columnRightElement) {
            columnRightElement.classList.remove('hidden');
            columnRightElement.style.display = '';
        }
        if (tankListElement) {
            tankListElement.classList.remove('hidden');
            tankListElement.style.display = '';
        }
    } else {
        console.log(`Country filter selected: ${countryValue}. No tank was selected.`);
    }

    if (state.selectedCountry === countryValue || countryValue === 'all') {
        state.selectedCountry = 'all';
        state.countrySelectedManually = false;
    } else {
        state.selectedCountry = countryValue;
        state.countrySelectedManually = true;
    }

    state.selectedCategory = 'all';
    state.categorySelectedManually = false;
    state.searchQuery = '';
    if (searchInput) searchInput.value = '';

    applyFiltersAndRenderTankList();
    updateFilterSelection('country', state.selectedCountry);
    updateFilterSelection('category', state.selectedCategory);
}

// Handle category filter selection
function handleCategorySelection(categoryValue) {
    if (state.selectedTank) {
        console.log(`Category filter '${categoryValue}' clicked. Tank '${state.selectedTank.name}' was selected. Resetting view.`);
        state.selectedTank = null;
        localStorage.removeItem('selectedTank');

        window.location.hash = '';
        history.replaceState({ tankListVisible: true, tankSelected: null }, "", window.location.pathname + window.location.search);

        if (tankCharacteristicsContainer) {
            tankCharacteristicsContainer.classList.add('hidden');
            tankCharacteristicsContainer.style.display = 'none';
        }
        if (columnRightElement) {
            columnRightElement.classList.remove('hidden');
            columnRightElement.style.display = '';
        }
        if (tankListElement) {
            tankListElement.classList.remove('hidden');
            tankListElement.style.display = '';
        }
    } else {
        console.log(`Category filter selected: ${categoryValue}. No tank was selected.`);
    }

    const internalCategory = categoryValue === 'all' ? 'all' : (RUSSIAN_TO_INTERNAL_TYPE_MAP[categoryValue] || ENGLISH_TO_INTERNAL_TYPE_MAP[categoryValue] || categoryValue);

    if (state.selectedCategory === internalCategory || internalCategory === 'all') {
        state.selectedCategory = 'all';
        state.categorySelectedManually = false;
        updateFilterSelection('category', 'all');
    } else {
        state.selectedCategory = internalCategory;
        state.categorySelectedManually = true;
        updateFilterSelection('category', categoryValue);
    }

    state.searchQuery = '';
    if (searchInput) searchInput.value = '';

    applyFiltersAndRenderTankList();
}

// Handle tank selection from the list
async function handleTankSelection(tankName, forceSelect = false) {
    const tank = state.allTanks.find(t => t.name === tankName);

    if (!tank) {
        console.error(`Tank ${tankName} not found.`);
        return;
    }

    const isSelectedTank = state.selectedTank && state.selectedTank.name === tank.name;

    if (state.restoringTankFromReload) {
        console.log('Восстановление танка после F5, устанавливаем forceSelect');
        forceSelect = true;
    }

    if (isSelectedTank && !forceSelect) {
        state.selectedTank = null;
        localStorage.removeItem('selectedTank');

        if (tankCharacteristicsContainer) {
            tankCharacteristicsContainer.classList.add('hidden');
            tankCharacteristicsContainer.style.display = 'none';
        }

        if (tankListElement) {
            tankListElement.classList.remove('hidden');
            tankListElement.style.display = '';
        }

        return;
    }

    state.selectedTank = tank;

    const tankToSave = {
        name: tank.name,
        country: tank.country,
        type: tank.type,
        countrySelectedManually: state.countrySelectedManually,
        categorySelectedManually: state.categorySelectedManually,
        tankId: tank.id
    };
    localStorage.setItem('selectedTank', JSON.stringify(tankToSave));

    if (tank.country) {
        state.selectedCountry = tank.country;
        const countryForUI = getCountryForUI(tank.country);
        updateFilterSelection('country', countryForUI);
    }
    if (tank.type) {
        const internalType = RUSSIAN_TO_INTERNAL_TYPE_MAP[tank.type] || ENGLISH_TO_INTERNAL_TYPE_MAP[tank.type] || tank.type;
        state.selectedCategory = internalType;
        updateFilterSelection('category', tank.type);
    }

    if (tankCharacteristicsContainer) {
        tankCharacteristicsContainer.classList.remove('hidden');
        tankCharacteristicsContainer.style.display = 'block';

        const characteristicsGrid = document.querySelector('.characteristics-grid');
        if (characteristicsGrid) {
            characteristicsGrid.style.display = 'flex';

            const columns = characteristicsGrid.querySelectorAll('.characteristics-column');
            columns.forEach(column => {
                column.style.display = 'block';
                column.classList.remove('hidden');

                const header = column.querySelector('.characteristics-header');
                const content = column.querySelector('.characteristics-content');

                if (header) header.style.display = 'flex';
                if (content) content.style.display = 'block';
            });
        }
    } else { 
        console.error("handleTankSelection: tankCharacteristicsContainer not found for showing!"); 
    }

    if (tankListElement) {
        tankListElement.classList.add('hidden');
        tankListElement.style.display = 'none';
    } else { 
        console.error("handleTankSelection: List container (#tank-list) not found!"); 
    }

    import('./components/tank-details/TankDetails.js').then(module => {
        module.updateTankDetailsUI(tank);
        console.log('🔄 handleTankSelection: TankDetails.updateTankDetailsUI вызвана');
    }).catch(error => {
        console.error('Error loading TankDetails module:', error);
    });

    if (!forceSelect) {
        localStorage.setItem('selectedTank', JSON.stringify({
            name: tank.name,
            country: tank.country,
            type: tank.type
        }));
        history.pushState({ tankName: tank.name }, `Tank: ${tank.name}`, `#${encodeURIComponent(tank.name)}`);
    }

    state.restoringTankFromReload = false;
}

// Removed unused function: handleSearch

// Оптимизированная функция поиска
function handleSearchOptimized(event) {
    const query = event.target.value.trim();

    if (state.searchQuery === query.toLowerCase()) return;

    console.log(`[Search] Optimized search for: "${query}"`);

    const startTime = performance.now();
    state.searchQuery = query.toLowerCase();

    if (query.length <= 2 && query.length > 0) {
        applyFiltersAndRenderTankList();
    } else {
        applyFiltersAndRenderTankList();
    }

    const endTime = performance.now();
    console.log(`[Search] Search completed in ${(endTime - startTime).toFixed(2)}ms`);
}

// --- Modal Logic ---
function _showModal(tankName, buildName) {
    if (!modal || !modalTitle || !modalBody) {
        console.error("Modal elements not found in cache.");
        return;
    }
    const tank = state.allTanks.find(t => t.name === tankName);
    if (!tank || !tank.bestBuilds) return;

    const build = tank.bestBuilds.find(b => b.name === buildName);
    if (!build) return;

    modalTitle.textContent = `Сборка "${build.name}" для ${tank.name}`;

    let content = '';
    if (tank.equipmentSlots && tank.equipmentSlots.length) {
        content += `<div class="modal-section"><h3>Слоты оборудования</h3><ul class="modal-list">` +
            tank.equipmentSlots.map((slot, idx) => `
                <li>Слот ${slot.id}: ${slot.category || 'Любой'} - ${(build.equipment && build.equipment[idx]) || 'Пусто'}</li>
            `).join('') +
            `</ul></div>`;
    }
    if (build.equipment && build.equipment.length) {
        const equipmentHTML = build.equipment.map(item => {
            const tankEq = tank.equipment?.find(e => e.name === item);
            const eqData = tankEq || {};
            const icon = eqData.icon || `src/assets/images/equipment/${item.replace(/\s+/g,'_')}.png`;
            return `<li><img src="${icon}" alt="${item}" class="equipment-icon" /> ${item}</li>`;
        }).join('');
        content += `<div class="modal-section"><h3>Оборудование</h3><ul class="modal-list">${equipmentHTML}</ul></div>`;
    }
    if (build.upgrades && build.upgrades.length) {
        content += `<div class="modal-section"><h3>Модули</h3><ul class="modal-list">${
            build.upgrades.map(item => `<li>${item}</li>`).join('')
        }</ul></div>`;
    }
    if (build.consumables && build.consumables.length) {
        content += `<div class="modal-section"><h3>Расходники</h3><ul class="modal-list">${
            build.consumables.map(item => `<li>${item}</li>`).join('')
        }</ul></div>`;
    }
    if (build.crewSkills && build.crewSkills.length) {
        content += `<div class="modal-section"><h3>Навыки экипажа</h3><ul class="modal-list">${
            build.crewSkills.map(skill => `<li>${skill}</li>`).join('')
        }</ul></div>`;
    }
    if (build.note) {
        content += `<div class="modal-section"><h3>Примечание</h3><p>${build.note}</p></div>`;
    }
    modalBody.innerHTML = content;
    modal.classList.remove('hidden');
    modal.setAttribute('aria-hidden', 'false');
    if(closeModalBtn) closeModalBtn.focus();
    else modal.focus();
}

function closeModal() {
    if (modal) {
        modal.classList.add('hidden');
        modal.setAttribute('aria-hidden', 'true');
    }
}

// --- Event Delegation Setup ---
function setupEventDelegation() {
    console.log("Setting up event delegation...");
    if (tankListElement) {
        tankListElement.addEventListener('click', (event) => {
            const tankItem = event.target.closest('.tank-item');
            if (tankItem) {
                const tankName = tankItem.dataset.tankName;
                if (tankName) {
                    window.location.hash = `#${encodeURIComponent(tankName)}`;
                }
            }
        });
    } else {
        console.warn("Tank list element not found for event delegation.");
    }

    if (countryFilters) {
        countryFilters.forEach(filterItem => {
            filterItem.addEventListener('click', (event) => {
                event.preventDefault();
                const countryValue = filterItem.dataset.country;
                if (countryValue) {
                    handleCountrySelection(countryValue);
                } else {
                    console.warn("Country filter item clicked but data-country attribute is missing or empty.", filterItem);
                }
            });
        });
    } else {
        console.warn("Country filter items not found during setupEventDelegation. 'countryFilters' is null.");
    }

    if (categoryFilters) {
        categoryFilters.forEach(filterItem => {
            filterItem.addEventListener('click', (event) => {
                event.preventDefault();
                const categoryValue = filterItem.dataset.category;
                if (categoryValue) {
                    handleCategorySelection(categoryValue);
                } else {
                    console.warn("Category filter item clicked but data-category attribute is missing or empty.", filterItem);
                }
            });
        });
    } else {
        console.warn("Category filter items not found during setupEventDelegation. 'categoryFilters' is null.");
    }

    if (searchInput) {
        // Используем оптимизированный debounced поиск
        const optimizedSearch = getDebounced('search', handleSearchOptimized, 200);
        searchInput.addEventListener('input', optimizedSearch);

        searchInput.addEventListener('focus', () => {
            if (!searchIndex) {
                console.log('[Search] 🔍 Preloading search index on focus');
                searchIndex = new TankSearchIndex(state.allTanks);
            }
        });
    }

    if (modal && closeModalBtn) {
        closeModalBtn.addEventListener('click', closeModal);
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                closeModal();
            }
        });
    }

    if (notesToggleButton) {
        notesToggleButton.addEventListener('click', toggleNotesVisibility);
    }

    console.log("Event delegation setup complete.");
}

// Utility to toggle visibility of the notes section
function toggleNotesVisibility() {
    if (!notesSection) {
        console.warn('toggleNotesVisibility: notesSection not cached.');
        return;
    }
    const nowHidden = notesSection.classList.toggle('hidden');
    notesSection.style.display = nowHidden ? 'none' : 'block';
}

// Function to apply current filters and render the tank list
function applyFiltersAndRenderTankList(keepHidden = false, forceRender = false) {
    // Проверяем активную вкладку из DOM, а не из state
    const vehiclesTab = document.querySelector('[data-section="vehicles"]');
    const isVehiclesActive = vehiclesTab && vehiclesTab.classList.contains('active');

    // Предотвращаем множественный рендеринг при инициализации
    if (!forceRender && !isInitialRenderComplete && isVehiclesActive) {
        console.log('[Render] Skipping render during initialization to prevent flickering');
        return;
    }

    // Скрываем только если явно указано или если vehicles НЕ активна
    const hideForOtherTabs = !isVehiclesActive && state.currentMenuName && state.currentMenuName !== 'vehicles';
    const finalKeepHidden = keepHidden || hideForOtherTabs;

    const filteredTanks = getFilteredTanks();
    const selectedTankName = state.selectedTank ? state.selectedTank.name : null;

    const detailsContainer = document.getElementById('tank-characteristics-container');
    if (detailsContainer) {
        detailsContainer.classList.add('hidden');
        detailsContainer.style.display = 'none';
        detailsContainer.style.opacity = '0';
        detailsContainer.style.visibility = 'hidden';
    }

    console.log(`Отображаем ${filteredTanks.length} танков после фильтрации`);

    renderTankList(filteredTanks, window.tanksData || state.allTanks, handleTankSelection, selectedTankName);

    const countryForUI = getCountryForUI(state.selectedCountry);
    updateFilterSelection('country', countryForUI);
    const categoryForUI = Object.keys(RUSSIAN_TO_INTERNAL_TYPE_MAP).find(key =>
        RUSSIAN_TO_INTERNAL_TYPE_MAP[key] === state.selectedCategory
    ) || state.selectedCategory;
    updateFilterSelection('category', categoryForUI);

    if (tankListElement) {
        if (finalKeepHidden) {
            tankListElement.classList.add('hidden');
            tankListElement.classList.remove('loaded');
            tankListElement.style.display = 'none';
            tankListElement.style.visibility = 'hidden';
        } else {
            tankListElement.classList.remove('hidden');
            tankListElement.style.display = 'grid';
            tankListElement.style.visibility = 'visible';

            // Добавляем класс loaded для плавного появления
            setTimeout(() => {
                tankListElement.classList.add('loaded');
            }, 50);
        }
    } else {
        console.error("applyFiltersAndRenderTankList: List container (#tank-list) not found!");
    }
}

// Diagnostic function to check app state
function diagnoseApp() {
    console.log('=== App Diagnosis ===');
    console.log('Sidebar element:', document.querySelector('.sidebar'));
    console.log('Menu items:', document.querySelectorAll('.sidebar-menu-item').length);
    console.log('Flag section:', document.getElementById('flag-section'));
    console.log('Tank list:', document.getElementById('tank-list'));
    console.log('State:', state);
    console.log('All tanks count:', state.allTanks ? state.allTanks.length : 'undefined');
    console.log('===================');
}

// Инициализация кастомных дропдаунов
function initializeCustomDropdowns() {
    const dropdowns = document.querySelectorAll('.custom-dropdown');

    dropdowns.forEach(dropdown => {
        const selected = dropdown.querySelector('.dropdown-selected');
        const optionElements = dropdown.querySelectorAll('.dropdown-option');

        const currentValue = selected.dataset.value;
        optionElements.forEach(option => {
            if (option.dataset.value === currentValue) {
                option.classList.add('selected');
            }
        });

        selected.addEventListener('click', (e) => {
            e.stopPropagation();

            dropdowns.forEach(otherDropdown => {
                if (otherDropdown !== dropdown) {
                    otherDropdown.classList.remove('active');
                    otherDropdown.querySelector('.dropdown-selected').classList.remove('active');
                }
            });

            dropdown.classList.toggle('active');
            selected.classList.toggle('active');
        });

        optionElements.forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();

                const value = option.dataset.value;
                const text = option.textContent.replace('✓', '').trim();
                const dropdownType = dropdown.dataset.dropdown;

                selected.dataset.value = value;
                selected.querySelector('.dropdown-text').textContent = text;

                optionElements.forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');

                dropdown.classList.remove('active');
                selected.classList.remove('active');

                handleDropdownChange(dropdownType, value);
            });
        });
    });

    document.addEventListener('click', () => {
        dropdowns.forEach(dropdown => {
            dropdown.classList.remove('active');
            dropdown.querySelector('.dropdown-selected').classList.remove('active');
        });
    });
}

// Обработчик изменений в дропдаунах
function handleDropdownChange(type, value) {
    switch(type) {
        case 'theme':
            handleThemeChange(value);
            break;
        case 'language':
            handleLanguageChange(value);
            break;
        case 'font-size':
            handleFontSizeChange(value);
            break;
    }
}

// Обработчики для настроек
function handleThemeChange(theme) {
    console.log('Theme changed to:', theme);
    localStorage.setItem('theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
    
    if (theme === 'dark') {
        document.body.classList.add('gray-bg');
    } else {
        document.body.classList.remove('gray-bg');
    }
}

function handleLanguageChange(language) {
    console.log('Language changed to:', language);
    localStorage.setItem('language', language);
    // Здесь будет логика смены языка
}

function handleFontSizeChange(fontSize) {
    console.log('Font size changed to:', fontSize);
    localStorage.setItem('fontSize', fontSize);
    
    const fontSizeMap = {
        'small': '14px',
        'medium': '16px',
        'large': '18px'
    };
    
    document.documentElement.style.setProperty('--base-font-size', fontSizeMap[fontSize] || '16px');
}

// Инициализация обработчиков настроек
function initializeSettingsHandlers() {
    // Обработчик для кнопки очистки кэша
    const clearCacheBtn = document.getElementById('clear-cache-btn');
    if (clearCacheBtn) {
        clearCacheBtn.addEventListener('click', () => {
            if (confirm('Вы уверены, что хотите очистить весь кэш приложения?')) {
                localStorage.clear();
                sessionStorage.clear();
                
                // Очистка кэша изображений
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }
                
                alert('Кэш успешно очищен. Страница будет перезагружена.');
                location.reload();
            }
        });
    }

    // Обработчик для переключателя кэширования
    const cacheToggle = document.getElementById('cache-toggle');
    if (cacheToggle) {
        // Загружаем сохраненное состояние
        const cacheEnabled = localStorage.getItem('cacheEnabled') !== 'false';
        cacheToggle.checked = cacheEnabled;

        cacheToggle.addEventListener('change', (e) => {
            const enabled = e.target.checked;
            localStorage.setItem('cacheEnabled', enabled);
            console.log('Cache enabled:', enabled);
        });
    }

    // Загружаем сохраненные настройки при инициализации
    const savedTheme = localStorage.getItem('theme') || 'dark';
    const savedLanguage = localStorage.getItem('language') || 'ru';
    const savedFontSize = localStorage.getItem('fontSize') || 'medium';

    // Применяем сохраненные настройки к дропдаунам
    const themeDropdown = document.querySelector('[data-dropdown="theme"]');
    if (themeDropdown) {
        const selected = themeDropdown.querySelector('.dropdown-selected');
        const option = themeDropdown.querySelector(`[data-value="${savedTheme}"]`);
        if (selected && option) {
            selected.dataset.value = savedTheme;
            selected.querySelector('.dropdown-text').textContent = option.textContent.trim();
            themeDropdown.querySelectorAll('.dropdown-option').forEach(opt => {
                opt.classList.toggle('selected', opt.dataset.value === savedTheme);
            });
        }
    }

    const languageDropdown = document.querySelector('[data-dropdown="language"]');
    if (languageDropdown) {
        const selected = languageDropdown.querySelector('.dropdown-selected');
        const option = languageDropdown.querySelector(`[data-value="${savedLanguage}"]`);
        if (selected && option) {
            selected.dataset.value = savedLanguage;
            selected.querySelector('.dropdown-text').textContent = option.textContent.trim();
            languageDropdown.querySelectorAll('.dropdown-option').forEach(opt => {
                opt.classList.toggle('selected', opt.dataset.value === savedLanguage);
            });
        }
    }

    const fontSizeDropdown = document.querySelector('[data-dropdown="font-size"]');
    if (fontSizeDropdown) {
        const selected = fontSizeDropdown.querySelector('.dropdown-selected');
        const option = fontSizeDropdown.querySelector(`[data-value="${savedFontSize}"]`);
        if (selected && option) {
            selected.dataset.value = savedFontSize;
            selected.querySelector('.dropdown-text').textContent = option.textContent.trim();
            fontSizeDropdown.querySelectorAll('.dropdown-option').forEach(opt => {
                opt.classList.toggle('selected', opt.dataset.value === savedFontSize);
            });
        }
    }

    // Применяем настройки
    handleThemeChange(savedTheme);
    handleFontSizeChange(savedFontSize);
}

// Система сравнения танков
const compareSystem = {
    tanks: [null, null],

    addTank(tank, slotIndex) {
        if (slotIndex < 0 || slotIndex > 1) return false;

        const tankWithId = tank.id ? tank : { ...tank, id: createTankId(tank.name) };
        this.tanks[slotIndex] = tankWithId;
        this.updateSlotDisplay(slotIndex);
        this.updateCompareButton();
        return true;
    },

    removeTank(slotIndex) {
        if (slotIndex < 0 || slotIndex > 1) return false;

        this.tanks[slotIndex] = null;
        this.updateSlotDisplay(slotIndex);
        this.updateCompareButton();
        return true;
    },

    clearAll() {
        this.tanks = [null, null];
        this.updateSlotDisplay(0);
        this.updateSlotDisplay(1);
        this.updateCompareButton();
        this.hideResults();
    },

    updateSlotDisplay(slotIndex) {
        const slot = document.querySelector(`[data-slot="${slotIndex + 1}"]`);
        if (!slot) return;

        const content = slot.querySelector('.tank-slot-content');
        const tank = this.tanks[slotIndex];

        if (tank) {
            const typeClass = getTankTypeClass(tank.type);
            const flagPath = getFlagPath(tank.country);

            content.innerHTML = `
                <div class="modern-tank-card">
                    <div class="tank-card-background"></div>
                    <div class="tank-card-content">
                        <div class="tank-card-top">
                            <div class="tank-icon-area">
                                <img src="src/assets/images/tanks/${tank.id}.webp" alt="${tank.name}" class="tank-card-icon" onerror="this.style.display='none'">
                            </div>
                            <div class="tank-flag-area">
                                <img src="${flagPath}" alt="${tank.country}" class="tank-card-flag">
                            </div>
                        </div>
                        <div class="tank-card-bottom">
                            <div class="tank-card-name">${tank.name}</div>
                            <div class="tank-card-badge ${typeClass}">${tank.type}</div>
                        </div>
                    </div>
                    <button class="tank-card-remove" onclick="compareSystem.removeTank(${slotIndex})" title="Удалить танк">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
            `;
            slot.classList.add('has-tank');
        } else {
            content.innerHTML = `
                <div class="tank-placeholder">
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                    </svg>
                    <span class="placeholder-text">Выберите танк</span>
                </div>
            `;
            slot.classList.remove('has-tank');
        }
    },

    updateCompareButton() {
        const compareBtn = document.getElementById('compare-btn');
        if (!compareBtn) return;

        const hasBothTanks = this.tanks[0] && this.tanks[1];
        compareBtn.disabled = !hasBothTanks;

        if (hasBothTanks) {
            compareBtn.textContent = 'Сравнить характеристики';
        } else {
            compareBtn.textContent = 'Выберите два танка для сравнения';
        }
    },

    showResults() {
        const resultsArea = document.getElementById('compare-results');
        if (!resultsArea) return;

        resultsArea.classList.remove('hidden');
        this.generateComparisonTable();
    },

    hideResults() {
        const resultsArea = document.getElementById('compare-results');
        if (!resultsArea) return;

        resultsArea.classList.add('hidden');
    },

    generateComparisonTable() {
        const resultsContent = document.querySelector('.results-content');
        if (!resultsContent || !this.tanks[0] || !this.tanks[1]) return;

        const tank1 = this.tanks[0];
        const tank2 = this.tanks[1];

        // Функция для безопасного получения значения характеристики
        const _getCharValue = (tank, path, defaultValue = 'Н/Д') => {
            const keys = path.split('.');
            let value = tank.characteristics || tank;
            
            for (const key of keys) {
                if (value && typeof value === 'object') {
                    value = value[key];
                } else {
                    return defaultValue;
                }
            }
            
            return value !== undefined && value !== null ? value : defaultValue;
        };

        resultsContent.innerHTML = `
            <div class="comparison-table">
                <div class="comparison-header">
                    <div class="comparison-category">Характеристика</div>
                    <div class="comparison-tank">
                        <div class="comparison-tank-icon-wrapper">
                            <img src="src/assets/images/tanks/${tank1.id}.webp" alt="${tank1.name}" class="comparison-tank-icon" onerror="this.style.display='none'">
                        </div>
                        <div class="comparison-tank-info">
                            <div class="comparison-tank-header">
                                <img src="${getFlagPath(tank1.country)}" alt="${tank1.country}" class="comparison-flag">
                                <div class="comparison-tank-badge ${getTankTypeClass(tank1.type)}">${tank1.type}</div>
                            </div>
                            <span class="comparison-tank-name">${tank1.name}</span>
                        </div>
                    </div>
                    <div class="comparison-tank">
                        <div class="comparison-tank-icon-wrapper">
                            <img src="src/assets/images/tanks/${tank2.id}.webp" alt="${tank2.name}" class="comparison-tank-icon" onerror="this.style.display='none'">
                        </div>
                        <div class="comparison-tank-info">
                            <div class="comparison-tank-header">
                                <img src="${getFlagPath(tank2.country)}" alt="${tank2.country}" class="comparison-flag">
                                <div class="comparison-tank-badge ${getTankTypeClass(tank2.type)}">${tank2.type}</div>
                            </div>
                            <span class="comparison-tank-name">${tank2.name}</span>
                        </div>
                    </div>
                </div>

                ${this.generateComparisonRow('Урон', tank1, tank2, 'damage', '')}
                ${this.generateComparisonRow('Бронепробитие', tank1, tank2, 'penetration', ' мм')}
                ${this.generateComparisonRow('Прочность', tank1, tank2, 'hitPoints', ' HP')}
                ${this.generateComparisonRow('Максимальная скорость', tank1, tank2, 'speed', ' км/ч')}
                ${this.generateComparisonRow('Обзор', tank1, tank2, 'viewRange', ' м')}
                ${this.generateComparisonRow('Время перезарядки', tank1, tank2, 'reloadTime', ' сек', true)}
                ${this.generateComparisonRow('Время сведения', tank1, tank2, 'aimTime', ' сек', true)}
                ${this.generateComparisonRow('Разброс', tank1, tank2, 'dispersion', ' м', true)}
                ${this.generateComparisonRow('Мощность двигателя', tank1, tank2, 'enginePower', ' л.с.')}
                ${this.generateComparisonRow('Скорость поворота', tank1, tank2, 'traverse', ' °/сек')}
            </div>
        `;
    },

    generateComparisonRow(label, tank1, tank2, stat, unit = '', isLowerBetter = false) {
        const getStatValue = (tank, stat, defaultValue = 'Н/Д') => {
            // Проверяем сначала в characteristics
            if (tank.characteristics && tank.characteristics[stat] !== undefined && tank.characteristics[stat] !== null) {
                return tank.characteristics[stat];
            }
            // Затем проверяем в самом объекте танка
            if (tank[stat] !== undefined && tank[stat] !== null) {
                return tank[stat];
            }
            return defaultValue;
        };

        const val1 = getStatValue(tank1, stat);
        const val2 = getStatValue(tank2, stat);

        let class1 = 'equal', class2 = 'equal';

        if (val1 !== 'Н/Д' && val2 !== 'Н/Д') {
            const num1 = parseFloat(val1);
            const num2 = parseFloat(val2);
            
            if (!isNaN(num1) && !isNaN(num2)) {
                if (isLowerBetter) {
                    class1 = num1 < num2 ? 'better' : num1 > num2 ? 'worse' : 'equal';
                    class2 = num2 < num1 ? 'better' : num2 > num1 ? 'worse' : 'equal';
                } else {
                    class1 = num1 > num2 ? 'better' : num1 < num2 ? 'worse' : 'equal';
                    class2 = num2 > num1 ? 'better' : num2 < num1 ? 'worse' : 'equal';
                }
            }
        }

        const displayVal1 = val1 === 'Н/Д' ? val1 : val1 + unit;
        const displayVal2 = val2 === 'Н/Д' ? val2 : val2 + unit;

        return `
            <div class="comparison-row">
                <div class="comparison-category">${label}</div>
                <div class="comparison-value ${class1}">${displayVal1}</div>
                <div class="comparison-value ${class2}">${displayVal2}</div>
            </div>
        `;
    }
};

// Сделаем compareSystem глобальным для доступа из HTML
window.compareSystem = compareSystem;

// Инициализация системы сравнения
function initializeCompareSystem() {
    const compareBtn = document.getElementById('compare-btn');
    const clearBtn = document.getElementById('clear-compare-btn');

    if (compareBtn) {
        compareBtn.addEventListener('click', () => {
            if (!compareBtn.disabled) {
                compareSystem.showResults();
            }
        });
    }

    if (clearBtn) {
        clearBtn.addEventListener('click', () => {
            compareSystem.clearAll();
        });
    }

    const tankSlots = document.querySelectorAll('.compare-tank-slot');
    tankSlots.forEach((slot, index) => {
        slot.addEventListener('click', (e) => {
            if (e.target.closest('.tank-card-remove')) return;
            openTankSelector(index);
        });
    });

    compareSystem.updateCompareButton();
}

// Открыть селектор танков
function openTankSelector(slotIndex) {
    const modal = document.createElement('div');
    modal.className = 'tank-selector-modal';
    modal.innerHTML = `
        <div class="tank-selector-content">
            <div class="tank-selector-header">
                <h3>Выберите танк для слота ${slotIndex + 1}</h3>
                <button class="close-selector-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="tank-selector-search">
                <input type="text" placeholder="Поиск танков..." class="tank-search-input">
            </div>
            <div class="tank-selector-list">
                <!-- Список танков будет добавлен через JavaScript -->
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    populateTankSelector(modal, slotIndex);

    const closeBtn = modal.querySelector('.close-selector-btn');
    closeBtn.addEventListener('click', () => {
        document.body.removeChild(modal);
    });

    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            document.body.removeChild(modal);
        }
    });

    const searchInput = modal.querySelector('.tank-search-input');
    searchInput.addEventListener('input', (e) => {
        filterTankSelector(modal, e.target.value, slotIndex);
    });

    setTimeout(() => searchInput.focus(), 100);
}

// Получить танки с ID для системы сравнения
function getTanksWithIds() {
    const tanks = state.allTanks || [];
    return tanks.map(tank => ({
        ...tank,
        id: createTankId(tank.name)
    }));
}



// Получить иконку роли
function getRoleIcon(roleName) {
    const roleMap = {
        'Штурмовой': 'assault.png',
        'Универсальный': 'universal.png',
        'Снайперский': 'sniper.png',
        'Прорыва': 'break.png',
        'Поддержки': 'support.png',
        'Колёсный': 'wheeled.png'
    };
    return roleMap[roleName] || 'universal.png';
}



// Создать HTML для танка в селекторе
function createTankSelectorHTML(tank) {
    const tierDisplay = tank.tier || 10;
    const typeClass = getTankTypeClass(tank.type);
    const roleName = tank.role ? tank.role.name : 'Универсальный';
    const roleIcon = getRoleIcon(roleName);
    const cost = tank.cost || null;
    const currencyIcon = tank.currencyIcon || 'src/assets/images/role/silver.png';
    const flagPath = getFlagPath(tank.country);

    return `
        <div class="tank-selector-item" data-tank-id="${tank.id}">
            <div class="selector-tank-icon-wrapper">
                <img src="src/assets/images/tanks/${tank.id}.webp" alt="${tank.name}" class="selector-tank-icon" onerror="this.style.display='none'">
            </div>
            <div class="selector-tank-info">
                <div class="selector-tank-header">
                    <img src="${flagPath}" alt="${tank.country}" class="selector-flag">
                    <div class="selector-tank-tier">Уровень ${tierDisplay}</div>
                </div>
                <div class="selector-tank-main">
                    <div class="selector-tank-name">${tank.name}</div>
                    <div class="selector-tank-badge ${typeClass}">${tank.type}</div>
                </div>
                <div class="selector-tank-meta">
                    <div class="selector-tank-role">
                        <img src="src/assets/images/role/${roleIcon}" alt="${roleName}" class="selector-role-icon">
                        <span>${roleName}</span>
                    </div>
                    ${cost ? `
                        <div class="selector-tank-cost">
                            <img src="${currencyIcon}" alt="currency" class="selector-currency-icon">
                            <span>${cost.toLocaleString()}</span>
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;
}

// Добавить обработчики клика для танков в селекторе
function addTankSelectorEventListeners(modal, tanks, slotIndex) {
    const tankItems = modal.querySelectorAll('.tank-selector-item');
    tankItems.forEach(item => {
        item.addEventListener('click', () => {
            const tankId = item.dataset.tankId;
            const tank = tanks.find(t => t.id === tankId);
            if (tank) {
                compareSystem.addTank(tank, slotIndex);
                document.body.removeChild(modal);
            }
        });
    });
}

// Заполнить селектор танков
function populateTankSelector(modal, slotIndex) {
    const tankList = modal.querySelector('.tank-selector-list');
    const tanks = getTanksWithIds();

    tankList.innerHTML = tanks.map(createTankSelectorHTML).join('');
    addTankSelectorEventListeners(modal, tanks, slotIndex);
}

// Фильтровать танки в селекторе
function filterTankSelector(modal, searchTerm, slotIndex) {
    const tanks = getTanksWithIds();
    const filteredTanks = tanks.filter(tank =>
        tank.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tank.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tank.type.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const tankList = modal.querySelector('.tank-selector-list');
    tankList.innerHTML = filteredTanks.map(createTankSelectorHTML).join('');
    addTankSelectorEventListeners(modal, tanks, slotIndex);
}

// Оптимизированная инициализация с lazy loading и батчингом
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('[Init] 🚀 Starting ULTRA-OPTIMIZED app initialization...');

        // Инициализируем оптимизации производительности в первую очередь
        initPerformanceOptimizations();
        initLazyLoader();
        initResourceManager();

        showLoadingIndicator();

        console.log('[Init] Phase 1: Critical components');
        initTheme();
        cacheDOMElements();

        console.log('[Init] Phase 2: Loading tank data');
        await initializeAllTanksCacheOptimized();

        console.log('[Init] Phase 3: Building search index');
        await buildSearchIndexAsync();

        console.log('[Init] Phase 4: UI components');
        await initializeUIComponents();

        console.log('[Init] Phase 5: Router and events');
        await initializeRouterAndEvents();

        console.log('[Init] Phase 6: Additional components');
        if (typeof requestIdleCallback !== 'undefined') {
            requestIdleCallback(() => {
                initializeAdditionalComponents();
            });
        } else {
            setTimeout(() => {
                initializeAdditionalComponents();
            }, 100);
        }

        hideLoadingIndicator();
        console.log('[Init] ✅ Optimized app initialization complete');

        window.applyFiltersAndRenderTankList = applyFiltersAndRenderTankList;
        window.getFilteredTanks = getFilteredTanks;
        window.renderTankList = renderTankList;
        console.log('[Init] Global functions exposed');

        // Дополнительная проверка состояния после полной инициализации (только если рендеринг не завершен)
        setTimeout(() => {
            if (!isInitialRenderComplete) {
                const vehiclesTab = document.querySelector('[data-section="vehicles"]');
                const tankListElement = document.getElementById('tank-list');

                if (vehiclesTab && vehiclesTab.classList.contains('active')) {
                    if (tankListElement && tankListElement.children.length === 0) {
                        console.log('[Init] 🔄 Tank list is empty after initialization, forcing final render');
                        isInitialRenderComplete = true;
                        window.isInitialRenderComplete = true;
                        applyFiltersAndRenderTankList(false, true);
                    } else if (tankListElement && tankListElement.style.display === 'none') {
                        console.log('[Init] 🔄 Tank list is hidden, making it visible');
                        tankListElement.style.display = 'grid';
                        tankListElement.style.opacity = '1';
                        tankListElement.style.visibility = 'visible';
                        tankListElement.classList.remove('hidden');
                        isInitialRenderComplete = true;
                        window.isInitialRenderComplete = true;
                    }
                }
            }
        }, 500);

    } catch (error) {
        console.error('[Init] ❌ Error during initialization:', error);
        hideLoadingIndicator();
        showErrorMessage('Ошибка загрузки приложения. Попробуйте обновить страницу.');
    }
});