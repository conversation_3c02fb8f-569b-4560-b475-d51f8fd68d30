/* ========================================
   TANK CHARACTERISTICS PAGE - OPTIMIZED
   ======================================== */

/* Base container */
.tank-characteristics-page {
    position: relative;
    width: 100%;
    min-height: 100vh;
    padding: 0.5rem;
    padding-bottom: 80px;
    margin: 0;
    color: #ffffff;
    background: transparent;
    box-sizing: border-box;
}

/* Content wrapper */
.tank-characteristics-content {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    padding: 0 0.5rem;
}

/* ======================================== 
   NAVIGATION 
   ======================================== */

.back-button-container {
    margin-bottom: 20px;
}

.back-to-list-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: #ffffff;
    font-size: 0.95rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.back-to-list-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateX(-3px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.back-to-list-btn svg {
    width: 20px;
    height: 20px;
}

/* ======================================== 
   TANK HEADER 
   ======================================== */

.tank-header {
    display: flex;
    justify-content: space-between;
    align-items: stretch;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 1.5rem auto;
    min-height: 120px;
    padding: 1.2rem;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

/* 🔥 Градиентная рамка для шапки */
.tank-header::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        #dc2626 0%,     /* Красный акцент */
        #1e3a8a 18%,    /* Темно-синий */
        #3730a3 32%,    /* Индиго */
        #7c3aed 46%,    /* Фиолетовый */
        #b45309 78%,    /* Темно-золотой */
        #92400e 100%    /* Коричнево-золотой */
    );
    border-radius: 22px;
    z-index: -1;
    opacity: 0.7;
    transition: opacity 0.3s ease;
}

/* Shimmer эффект при hover - ограничен границами */
.tank-header::after {
    content: '';
    position: absolute;
    top: 2px;
    left: -50%;
    width: 50%;
    height: calc(100% - 4px);
    background: linear-gradient(90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.2) 50%,
        transparent 100%
    );
    border-radius: 18px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 2;
}

.tank-header:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
}

.tank-header:hover::before {
    opacity: 1;
}

.tank-header:hover::after {
    opacity: 1;
    animation: shimmerOnHover 2s ease-in-out;
}

@keyframes shimmerOnHover {
    0% { left: -50%; }
    100% { left: 100%; }
}

.tank-header-left {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    flex: 1;
}

.tank-header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;
}

/* Tank icon */
.tank-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    flex-shrink: 0;
}

.tank-icon-large {
    width: 120px;
    height: 75px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    background: transparent;
}

/* Tank info */
.tank-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
    flex: 1;
}

.tank-name-row {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 14px;
}

.tank-name-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;
    min-width: 0;
    align-items: flex-start;
}

.tank-name-with-badge {
    display: inline-flex;
    align-items: center;
    gap: 14px;
    flex-wrap: nowrap;
    width: auto;
}

/* Tank name - КОМПАКТНЫЙ РАЗМЕР */
.tank-characteristics-page .tank-name {
    display: inline-block;
    margin: 0;
    font-size: clamp(1rem, 1.4vw, 1.3rem);
    font-weight: 700;
    background: linear-gradient(135deg, #ffffff, #ffffff, #f1f5f9);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    transition: all 0.3s ease;
}

/* Hover эффект для названий танков в характеристиках */
.tank-characteristics-page .tank-name:hover {
    background: linear-gradient(135deg, #ffffff, #ffffff, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 3px 8px rgba(0, 0, 0, 0.6);
    filter: contrast(1.2) brightness(1.2);
    transform: scale(1.02);
}

/* Tank type badge - СИНХРОНИЗИРОВАН С КАРТОЧКАМИ */
.tank-characteristics-page .tank-type-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 4px 8px;
    font-size: 0.85rem;
    font-weight: 700;
    color: #ffffff;
    text-transform: uppercase;
    line-height: 1;
    border-radius: 6px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    white-space: nowrap;
    flex-shrink: 0;
    transition: all 0.3s ease;
}

/* Красивые затемненные градиенты на основе оригинальных цветов */
.tank-type-badge.lt {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6, #6d28d9);
    border: 1px solid rgba(124, 58, 237, 0.5);
}

.tank-type-badge.st {
    background: linear-gradient(135deg, #db2777, #ec4899, #be185d);
    border: 1px solid rgba(219, 39, 119, 0.5);
}

.tank-type-badge.tt {
    background: linear-gradient(135deg, #2563eb, #3b82f6, #1d4ed8);
    border: 1px solid rgba(37, 99, 235, 0.5);
}

.tank-type-badge.td {
    background: linear-gradient(135deg, #059669, #10b981, #047857);
    border: 1px solid rgba(5, 150, 105, 0.5);
}

.tank-type-badge.spg {
    background: linear-gradient(135deg, #dc2626, #ef4444, #b91c1c);
    border: 1px solid rgba(220, 38, 38, 0.5);
}

/* Hover эффекты для бейджиков в характеристиках */
.tank-characteristics-page .tank-type-badge:hover {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    filter: brightness(1.1) saturate(1.1);
}

/* Tank flag */
.tank-flag-container {
    display: flex;
    align-items: center;
}

.tank-characteristics-page .tank-flag,
.tank-header .tank-flag {
    display: block;
    width: 80px;
    height: 48px;
    object-fit: contain;
    object-position: center;
    flex-shrink: 0;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Tank meta info */
.tank-meta {
    display: flex;
    gap: 14px;
    align-items: center;
    flex-wrap: nowrap;
    justify-content: flex-start;
}

.tank-role,
.tank-price {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 6px;
    font-weight: 600;
    white-space: nowrap;
    flex-shrink: 0;
    transition: all 0.3s ease;
    height: 28px; /* Фиксированная высота для одинакового размера */
}

.tank-role {
    background: rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(76, 175, 80, 0.4);
    color: #4CAF50;
}

.tank-price {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid rgba(255, 215, 0, 0.4);
    color: #FFD700;
}

.tank-role:hover,
.tank-price:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.role-icon,
.currency-icon {
    width: 18px;
    height: 18px;
    flex-shrink: 0;
}

.role-text {
    font-size: 0.8rem;
    font-weight: 600;
    background: linear-gradient(135deg, #ffffff, #f1f5f9, #cbd5e1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    white-space: nowrap;
}

.price-value {
    font-size: 0.8rem;
    font-weight: 600;
    background: linear-gradient(135deg, #ffffff, #f1f5f9, #cbd5e1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
    white-space: nowrap;
}

/* Equipment slots - КОМПАКТНОЕ ПОЗИЦИОНИРОВАНИЕ */

/* Позиционирование слотов оборудования с группировкой */
.tank-header .tank-header-right .equipment-slots {
    display: flex;
    align-items: center;
    gap: 4px;
    position: absolute;
    bottom: 5px;
    right: 10px;
}

/* Группировка слотов: 1 | 2-3-4 | 5 | 6-7-8 | 9 */
.equipment-slot.slot-1 {
    margin-right: 6px;
}

.equipment-slot.slot-4 {
    margin-right: 6px;
}

.equipment-slot.slot-5 {
    margin-right: 6px;
}

.equipment-slot.slot-8 {
    margin-right: 6px;
}

.equipment-slot {
    width: 61px;
    height: 61px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.equipment-slot::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.equipment-slot:hover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.2);
    transform: scale(1.05);
}

.equipment-slot:hover::before {
    opacity: 1;
}

.slot-background {
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* ======================================== 
   CHARACTERISTICS SECTIONS - УВЕЛИЧЕНЫ 
   ======================================== */

/* НОВЫЙ LAYOUT: Характеристики сверху, сборки и примечания снизу */
.characteristics-main-layout {
    width: 100%;
    max-width: 1200px;
    margin: 15px auto 1.5rem auto;
    padding: 0 10px;
    box-sizing: border-box;
}

/* Секция сборок и примечаний снизу */
.builds-and-notes-section {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto 2rem auto;
    padding: 0 10px;
    box-sizing: border-box;
    /* Предотвращаем скачки layout при анимации */
    contain: layout style;
    /* Стабилизация высоты контейнера */
    position: relative;
    /* Убираем transition для предотвращения конфликтов */
}

/* Удалено дублирующее определение .notes-icon */

.characteristics-sections {
    display: flex;
    flex-direction: row; /* ГОРИЗОНТАЛЬНОЕ расположение характеристик */
    gap: 15px; /* Зазор между секциями характеристик */
    width: 100%;
    box-sizing: border-box;
    flex-wrap: nowrap; /* Не переносим на новую строку */
}

.builds-and-notes-column {
    display: flex;
    flex-direction: column; /* Сборки сверху, примечания снизу */
    gap: 30px;
    width: 100%;
}

/* Таблица сборок - полная ширина 1200px */
.builds-section {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Примечания - полная ширина 1200px */
.notes-section {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.characteristics-section {
    min-width: 0;
    flex: 1;
    padding: 1.2rem;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    box-sizing: border-box;
    overflow: visible;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Оптимизация производительности */
    contain: layout style paint;
    will-change: transform, box-shadow;
    transform: translateZ(0);
}

.characteristics-section:hover {
    transform: translateY(-3px) translateZ(0);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header h2 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: #ffffff;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    white-space: nowrap;
    text-align: center;
}

/* Убираем стили для SVG иконок - теперь используем только эмодзи */

/* Section themes - УЛУЧШЕНЫ */
.armament {
    border-left: 5px solid #FF4757;
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.05), transparent);
}

.armament .section-header {
    border-bottom-color: rgba(255, 71, 87, 0.3);
    color: #FF4757;
}

.armament .characteristic-fill {
    background: linear-gradient(90deg, #FF4757, #FF6B7A);
    box-shadow: 0 0 12px rgba(255, 71, 87, 0.5);
}

.mobility {
    border-left: 5px solid #1DD1A1;
    background: linear-gradient(135deg, rgba(29, 209, 161, 0.05), transparent);
}

.mobility .section-header {
    border-bottom-color: rgba(29, 209, 161, 0.3);
    color: #1DD1A1;
}

.mobility .characteristic-fill {
    background: linear-gradient(90deg, #1DD1A1, #55EFC4);
    box-shadow: 0 0 12px rgba(29, 209, 161, 0.5);
}

.other {
    border-left: 5px solid #FFA726;
    background: linear-gradient(135deg, rgba(255, 167, 38, 0.05), transparent);
}

.other .section-header {
    border-bottom-color: rgba(255, 167, 38, 0.3);
    color: #FFA726;
}

.other .characteristic-fill {
    background: linear-gradient(90deg, #FFA726, #FFB74D);
    box-shadow: 0 0 12px rgba(255, 167, 38, 0.5);
}

/* Characteristics grid */
.characteristics-grid {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.characteristic-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.characteristic-item:last-child {
    border-bottom: none;
}

.characteristic-label {
    font-size: 0.8rem;
    font-weight: 600;
    color: #B8BCC8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.characteristic-value-container {
    display: flex;
    align-items: center;
    gap: 10px;
    justify-content: space-between;
}

.characteristic-bar {
    position: relative;
    flex: 1;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 4px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.characteristic-fill {
    position: relative;
    height: 100%;
    border-radius: 5px;
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.characteristic-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.2) 20%,
        rgba(255, 255, 255, 0.4) 50%,
        rgba(255, 255, 255, 0.2) 80%,
        transparent 100%
    );
    animation: smoothShimmer 3s ease-in-out infinite;
}

@keyframes smoothShimmer {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

.characteristic-value {
    min-width: 80px;
    font-size: 0.9rem;
    font-weight: 700;
    color: #ffffff;
    text-align: right;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* ======================================== 
   BUILDS SECTION - УЛУЧШЕНА ТАБЛИЦА 
   ======================================== */

.builds-section {
    width: 100%;
    max-width: 1200px; /* Ограничиваем ширину таблицы сборок */
    margin: 0 auto; /* Центрируем таблицу */
    /* Убираем фон - только таблица без прозрачного окна */
}

#equipment-section {
    width: 100%;
    max-width: 1400px;
    margin-top: 40px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.builds-header {
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.builds-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.builds-table-container {
    width: 100%;
    max-width: 1200px;
    margin: 15px 0;
    overflow-x: auto;
    overflow-y: hidden;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    -webkit-overflow-scrolling: touch;
    /* Оптимизация производительности */
    contain: layout style paint;
    will-change: scroll-position;
    transform: translateZ(0);
}

/* Скрываем скроллбар но оставляем функциональность */
.builds-table-container::-webkit-scrollbar {
    height: 6px;
}

.builds-table-container::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

.builds-table-container::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.builds-table-container::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Переопределяем все DaisyUI стили для чистой таблицы */
.builds-table-daisy {
    width: 100%;
    min-width: 1100px;
    margin: 0;
    font-family: 'Inter', Arial, sans-serif;
    font-size: 13px;
    color: #ffffff;
    background: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    table-layout: auto;
    border: none;
    box-shadow: none;
    border-radius: 0;
}

/* Убираем все DaisyUI классы */
.builds-table-daisy.table {
    background: transparent;
    border: none;
}

.builds-table-daisy.table-zebra {
    background: transparent;
}

.builds-table-daisy.bg-base-200 {
    background: transparent;
}

/* ПЕРЕОПРЕДЕЛЯЕМ DAISYUI СТИЛИ ДЛЯ ЗАГОЛОВКОВ */
.builds-table-daisy thead.bg-primary {
    background: transparent;
}

.builds-table-daisy thead.text-primary-content {
    color: #ffffff;
}

.builds-table-daisy th,
.builds-table-daisy td {
    padding: 8px 6px;
    height: 36px;
    font-size: 12px;
    line-height: 1.3;
    vertical-align: middle;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    border: inherit;
    background: inherit;
    box-sizing: border-box;
}

.builds-table-daisy thead {
    background: transparent;
}

.builds-table-daisy thead th {
    font-weight: 600;
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    border-radius: 0;
    position: relative;
}

/* ТАБЛИЦА С ЦВЕТАМИ ИЗ ПРИМЕЧАНИЙ - ОПТИМИЗИРОВАННАЯ */
/* Общие стили для всех ячеек */
.builds-table-daisy thead th,
.builds-table-daisy td {
    color: #ffffff;
}

/* Колонка 1 - Сборка */
.builds-table-daisy thead th:nth-child(1), 
.builds-table-daisy td:nth-child(1) {
    background: rgba(96, 165, 250, 0.15);
    border: 1px solid rgba(96, 165, 250, 0.3);
    width: 160px;
    text-align: left;
    padding: 8px 12px;
    white-space: normal;
    word-wrap: break-word;
}

/* Колонка 2 - Эффективность */
.builds-table-daisy thead th:nth-child(2), 
.builds-table-daisy td:nth-child(2) {
    background: rgba(139, 92, 246, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.3);
    width: 80px;
}

/* Колонки 3, 5, 9 - Вклад, Помощь, Сведение (зеленые) */
.builds-table-daisy thead th:nth-child(3), 
.builds-table-daisy td:nth-child(3),
.builds-table-daisy thead th:nth-child(5), 
.builds-table-daisy td:nth-child(5),
.builds-table-daisy thead th:nth-child(9), 
.builds-table-daisy td:nth-child(9) {
    background: rgba(34, 197, 94, 0.15);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.builds-table-daisy thead th:nth-child(3), 
.builds-table-daisy td:nth-child(3) { width: 55px; }

.builds-table-daisy thead th:nth-child(5), 
.builds-table-daisy td:nth-child(5) { width: 60px; }

.builds-table-daisy thead th:nth-child(9), 
.builds-table-daisy td:nth-child(9) { width: 70px; }

/* Колонка 4 - Урон */
.builds-table-daisy thead th:nth-child(4), 
.builds-table-daisy td:nth-child(4) {
    background: rgba(239, 68, 68, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.3);
    width: 60px;
}

/* Колонки 6, 11 - DPM, Дисперсия (желтые) */
.builds-table-daisy thead th:nth-child(6), 
.builds-table-daisy td:nth-child(6),
.builds-table-daisy thead th:nth-child(11), 
.builds-table-daisy td:nth-child(11) {
    background: rgba(245, 158, 11, 0.15);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.builds-table-daisy thead th:nth-child(6), 
.builds-table-daisy td:nth-child(6) { width: 60px; }

.builds-table-daisy thead th:nth-child(11), 
.builds-table-daisy td:nth-child(11) { width: 70px; }

/* Колонки 7, 13 - Разброс, Точность (розовые) */
.builds-table-daisy thead th:nth-child(7), 
.builds-table-daisy td:nth-child(7),
.builds-table-daisy thead th:nth-child(13), 
.builds-table-daisy td:nth-child(13) {
    background: rgba(236, 72, 153, 0.15);
    border: 1px solid rgba(236, 72, 153, 0.3);
}

.builds-table-daisy thead th:nth-child(7), 
.builds-table-daisy td:nth-child(7) { width: 65px; }

.builds-table-daisy thead th:nth-child(13), 
.builds-table-daisy td:nth-child(13) { width: 75px; }

/* Колонка 8 - Дин. разброс */
.builds-table-daisy thead th:nth-child(8), 
.builds-table-daisy td:nth-child(8) {
    background: rgba(249, 115, 22, 0.15);
    border: 1px solid rgba(249, 115, 22, 0.3);
    width: 80px;
}

/* Колонка 10 - Обзор */
.builds-table-daisy thead th:nth-child(10), 
.builds-table-daisy td:nth-child(10) {
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.3);
    width: 60px;
}

/* Колонка 12 - CV */
.builds-table-daisy thead th:nth-child(12), 
.builds-table-daisy td:nth-child(12) {
    background: rgba(139, 92, 246, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.3);
    width: 50px;
}

/* Скрываем все иконки сортировки */
.builds-table-daisy th .sort-icon,
.builds-table-daisy td .sort-icon {
    display: none;
}

/* Убираем все стили для иконок сортировки */

.builds-table th:last-child {
    border-right: none;
}

/* Специальная ширина для первой колонки (название сборки) */
.builds-table th:first-child {
    width: 20%; /* Больше места для названий сборок */
    text-align: left;
    padding-left: 16px;
}

/* Остальные колонки равномерно распределяются */
.builds-table th:not(:first-child) {
    width: calc(80% / 12); /* 12 колонок данных делят 80% ширины */
}

/* ПРОСТЫЕ СТИЛИ ДЛЯ ЗАГОЛОВКОВ ТАБЛИЦЫ - КАК В ПРИМЕЧАНИЯХ */
.builds-table-daisy thead th.th-build {
    background: rgba(96, 165, 250, 0.15);
    border: 1px solid rgba(96, 165, 250, 0.4);
    color: #60a5fa;
}

.builds-table-daisy thead th.th-efficiency {
    background: rgba(139, 92, 246, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.4);
    color: #8b5cf6;
}

.builds-table-daisy thead th.th-contribution {
    background: rgba(34, 197, 94, 0.15);
    border: 1px solid rgba(34, 197, 94, 0.4);
    color: #22c55e;
}

.builds-table-daisy thead th.th-damage {
    background: rgba(239, 68, 68, 0.15);
    border: 1px solid rgba(239, 68, 68, 0.4);
    color: #ef4444;
}

.builds-table-daisy thead th.th-assist {
    background: rgba(34, 197, 94, 0.15);
    border: 1px solid rgba(34, 197, 94, 0.4);
    color: #22c55e;
}

.builds-table-daisy thead th.th-dpm {
    background: rgba(245, 158, 11, 0.15);
    border: 1px solid rgba(245, 158, 11, 0.4);
    color: #f59e0b;
}

.builds-table-daisy thead th.th-spread {
    background: rgba(236, 72, 153, 0.15);
    border: 1px solid rgba(236, 72, 153, 0.4);
    color: #ec4899;
}

.builds-table-daisy thead th.th-dynspread {
    background: rgba(249, 115, 22, 0.15);
    border: 1px solid rgba(249, 115, 22, 0.4);
    color: #f97316;
}

.builds-table-daisy thead th.th-aimtime {
    background: rgba(34, 197, 94, 0.15);
    border: 1px solid rgba(34, 197, 94, 0.4);
    color: #22c55e;
}

.builds-table-daisy thead th.th-viewrange {
    background: rgba(59, 130, 246, 0.15);
    border: 1px solid rgba(59, 130, 246, 0.4);
    color: #3b82f6;
}

.builds-table-daisy thead th.th-dispersion {
    background: rgba(245, 158, 11, 0.15);
    border: 1px solid rgba(245, 158, 11, 0.4);
    color: #f59e0b;
}

.builds-table-daisy thead th.th-cv {
    background: rgba(139, 92, 246, 0.15);
    border: 1px solid rgba(139, 92, 246, 0.4);
    color: #8b5cf6;
}

.builds-table-daisy thead th.th-accuracy {
    background: rgba(236, 72, 153, 0.15);
    border: 1px solid rgba(236, 72, 153, 0.4);
    color: #ec4899;
}

/* Цветная линия под заголовком */
.builds-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: inherit;
    filter: brightness(1.3);
}

/* Строки тела таблицы */
.builds-table-daisy tbody {
    background: transparent;
}

.builds-table-daisy tbody tr {
    background: transparent;
    border: none;
}

.builds-table-daisy tbody tr:nth-child(even) {
    background: transparent;
}

.builds-table-daisy tbody tr:hover {
    background: transparent;
}

.builds-table-daisy tbody td {
    position: relative;
    overflow: hidden;
    padding: 8px 6px;
    vertical-align: middle;
    white-space: nowrap;
    z-index: 1;
    transition: all 0.2s ease;
    border: none;
    border-bottom: none;
    background: inherit;
}

.builds-table-daisy tbody tr:hover td {
    filter: brightness(1.05);
}

/* Первая колонка (название сборки) */
.builds-table-daisy td:first-child,
.builds-table-daisy th:first-child,
.td-build {
    font-weight: 600;
    text-align: left;
    padding-left: 12px;
    padding-right: 8px;
    font-size: 13px;
    white-space: normal;
    word-wrap: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
}

.build-link {
    color: #60a5fa;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.build-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: #60a5fa;
    transition: width 0.3s ease;
}

.build-link:hover {
    color: #93c5fd;
}

.build-link:hover::after {
    width: 100%;
}

/* No builds message */
.no-builds-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    margin: 30px 0;
    text-align: center;
    background: rgba(255, 255, 255, 0.03);
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: 16px;
}

.no-builds-message h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
}

.no-builds-message p {
    margin: 12px 0 0 0;
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

/* ========================================
   NOTES SECTION - КОМПАКТНЫЙ ДИЗАЙН
   ======================================== */

.notes-section {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 0;
    padding: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.notes-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    /* Добавляем анимацию для линии */
    transition: border-bottom-color 0.3s ease-out;
}

/* Анимация линии при скрытии примечаний */
.notes-section.notes-hidden .notes-header {
    border-bottom-color: transparent;
    transition: border-bottom-color 0.25s ease-in;
}

/* Убираем градиентную линию */
.notes-header::after {
    display: none;
}

.notes-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.notes-icon,
.notes-title .notes-icon,
svg.notes-icon {
    width: 24px;
    height: 24px;
    color: #FFA726;
    flex-shrink: 0;
    min-width: 24px;
    min-height: 24px;
}

.notes-title h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* КОМПАКТНЫЙ КОНТЕНТ ПРИМЕЧАНИЙ */
.notes-content-compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 8px;
}

/* ВОССТАНОВЛЕННЫЕ ПЛАВНЫЕ АНИМАЦИИ ДЛЯ ПРИМЕЧАНИЙ */
.notes-section {
    /* Базовые стили для показанного состояния */
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    max-height: 2000px; /* Достаточно большое значение для любого контента */

    /* ФИКСИРОВАННЫЕ размеры и отступы - НЕ ИЗМЕНЯЮТСЯ при анимации */
    height: auto;
    margin-bottom: 2rem;
    margin-top: 0;
    padding: 16px;

    /* ПЛАВНАЯ анимация всех свойств */
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                visibility 0.4s cubic-bezier(0.4, 0, 0.2, 1);

    /* Убираем will-change для предотвращения проблем с layout */
    overflow: visible;
}

.notes-section.notes-hidden {
    /* Плавное скрытие с анимацией высоты */
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transform: translateY(-10px);
    max-height: 0; /* Плавное схлопывание */

    /* СОХРАНЯЕМ отступы для плавности */
    margin-bottom: 2rem;
    margin-top: 0;
    padding: 16px;

    /* Более быстрая анимация скрытия */
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                transform 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                max-height 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                visibility 0.3s cubic-bezier(0.4, 0, 0.6, 1);

    overflow: hidden;
}

/* УЛУЧШЕННАЯ СИНХРОНИЗИРОВАННАЯ АНИМАЦИЯ ВСЕХ ЭЛЕМЕНТОВ ПРИМЕЧАНИЙ */
.notes-section .notes-content-compact {
    /* Плавная анимация содержимого */
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s,
                transform 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
    opacity: 1;
    transform: translateY(0);
}

.notes-section.notes-hidden .notes-content-compact {
    opacity: 0;
    transform: translateY(-8px);
    /* Быстрая анимация скрытия содержимого */
    transition: opacity 0.25s cubic-bezier(0.4, 0, 0.6, 1),
                transform 0.25s cubic-bezier(0.4, 0, 0.6, 1);
}

/* Заголовок примечаний - СИНХРОННО с основной анимацией */
.notes-section .notes-header {
    /* Плавная анимация заголовка */
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                transform 0.4s cubic-bezier(0.4, 0, 0.2, 1),
                border-bottom-color 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 1;
    transform: translateY(0);
}

.notes-section.notes-hidden .notes-header {
    opacity: 0;
    transform: translateY(-5px);
    border-bottom-color: transparent;
    /* Быстрая анимация скрытия заголовка */
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                transform 0.3s cubic-bezier(0.4, 0, 0.6, 1),
                border-bottom-color 0.3s cubic-bezier(0.4, 0, 0.6, 1);
}

/* НАСТРОЙКА ПЛАВНОГО СКРОЛЛА */
html, body {
    /* Разрешаем плавный скролл для программного скролла */
    scroll-behavior: smooth;
}

/* Убираем все дополнительные скроллбары - оставляем только основной */
html {
    /* Убираем скроллбар с html */
    overflow: hidden;
}

body {
    /* Убираем скроллбар с body */
    overflow: hidden;
}

/* Контейнер страницы танка - стабильная высота */
.tank-characteristics-page {
    /* Минимальная высота для предотвращения скачков */
    min-height: 100vh;
    position: relative;
    /* Убираем любые overflow стили - пусть родительский контейнер управляет скроллом */
    overflow: visible;
}

/* Контейнер сборок и примечаний - стабильная структура */
.builds-and-notes-section {
    /* Предотвращаем скачки layout */
    contain: layout style;
}

/* Оптимизация производительности для анимаций */
.notes-section,
.notes-section * {
    /* Изолируем слой для анимаций */
    will-change: auto;
}

.notes-section.notes-hidden {
    /* Активируем оптимизацию только во время анимации */
    will-change: transform, opacity, max-height;
}

/* Стабилизация для мобильных устройств */
@media (max-width: 768px) {
    html {
        /* На мобильных устройствах скроллбар может быть скрыт */
        overflow-y: auto;
    }
}

/* Убираем scroll margins и paddings только для примечаний */
.notes-section,
.notes-section * {
    scroll-margin: 0;
    scroll-margin-top: 0;
    scroll-margin-bottom: 0;
    scroll-padding: 0;
    scroll-padding-top: 0;
    scroll-padding-bottom: 0;
}

/* Настройки для пользователей с предпочтением уменьшенной анимации */
@media (prefers-reduced-motion: reduce) {
    html, body {
        scroll-behavior: auto;
    }

    /* Для примечаний оставляем быструю, но заметную анимацию */
    .notes-section {
        transition: opacity 0.2s ease,
                   transform 0.2s ease,
                   max-height 0.2s ease,
                   visibility 0.2s ease !important;
    }

    .notes-section .notes-content-compact,
    .notes-section .notes-header {
        transition: opacity 0.15s ease,
                   transform 0.15s ease !important;
    }
}


/* Стили для кнопки переключения примечаний */
.notes-toggle-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(255, 167, 38, 0.15);
    border: 1px solid rgba(255, 167, 38, 0.3);
    border-radius: 8px;
    color: #FFA726;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    user-select: none;
    margin-left: auto;
}

.notes-toggle-btn:hover {
    background: rgba(255, 167, 38, 0.25);
    border-color: rgba(255, 167, 38, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 167, 38, 0.2);
}

.notes-toggle-btn .toggle-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notes-toggle-btn.collapsed .toggle-icon {
    transform: rotate(180deg);
}

/* Стили для иконки переключения в таблице */
#toggle-notes-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

#toggle-notes-icon:hover {
    transform: scale(1.1);
    filter: brightness(1.2);
}

/* Улучшенный заголовок примечаний с кнопкой */
.notes-title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    position: relative;
}

.notes-title h3 {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.notes-toggle-btn {
    position: absolute;
    right: 0;
}

.note-item-compact {
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 0;
    transition: all 0.2s ease;
    position: relative;
}

.note-item-compact:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.note-text-compact {
    font-size: 0.85rem;
    font-weight: 400;
    color: #e2e8f0;
    line-height: 1.4;
    text-align: left;
}

.note-text-compact strong {
    font-weight: 600;
    font-size: 0.9rem;
    color: #60a5fa;
    margin-right: 4px;
}

/* СТАРЫЕ СТИЛИ ДЛЯ СОВМЕСТИМОСТИ */
.notes-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.note-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 16px 20px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.06) 0%,
        rgba(255, 255, 255, 0.03) 100%
    );
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.12);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.note-item:hover {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.06) 100%
    );
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px) translateX(4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.note-marker {
    margin-top: 2px;
    font-size: 1rem;
    font-weight: 600;
    color: #ff6b6b;
    flex-shrink: 0;
}

.note-text {
    font-size: 0.95rem;
    font-weight: 400;
    color: #e2e8f0;
    line-height: 1.5;
    text-align: left;
}

.note-text strong {
    font-weight: 700;
    font-size: 1.1rem;
    color: #60a5fa;
    display: inline-block;
    margin-bottom: 4px;
}

/* ЦВЕТНЫЕ КАТЕГОРИИ ДЛЯ КОМПАКТНОГО ДИЗАЙНА */
.note-text-compact .category-efficiency,
.note-text .category-efficiency {
    color: #8b5cf6; /* Фиолетовый - как в таблице */
}

.note-text-compact .category-contribution,
.note-text .category-contribution {
    color: #22c55e; /* Зеленый - как в таблице */
}

.note-text-compact .category-damage,
.note-text .category-damage {
    color: #ef4444; /* Красный - как в таблице */
}

.note-text-compact .category-spread,
.note-text .category-spread {
    color: #ec4899; /* Розовый - как в таблице */
}

.note-text-compact .category-dispersion,
.note-text .category-dispersion {
    color: #f59e0b; /* Желтый - как в таблице */
}

.note-text-compact .category-viewrange,
.note-text .category-viewrange {
    color: #3b82f6; /* Синий - как обзор в таблице */
}

.note-text-compact .category-accuracy,
.note-text .category-accuracy {
    color: #ec4899; /* Розовый - как в таблице */
}

.note-text-compact .category-aimtime,
.note-text .category-aimtime {
    color: #22c55e; /* Зеленый - как сведение в таблице */
}

.note-text-compact .category-help,
.note-text .category-help {
    color: #22c55e; /* Зеленый - как в таблице */
}

.note-text-compact .category-dpm,
.note-text .category-dpm {
    color: #f59e0b; /* Желтый - как в таблице */
}

.note-text-compact .category-dynspread,
.note-text .category-dynspread {
    color: #f97316; /* Оранжевый - как дин. разброс в таблице */
}

.note-text-compact .category-cv,
.note-text .category-cv {
    color: #8b5cf6; /* Фиолетовый - как CV в таблице */
}

/* ======================================== 
   RESPONSIVE DESIGN 
   ======================================== */

@media (max-width: 1400px) {
    .tank-characteristics-page {
        padding: 1rem;
    }

    .characteristics-main-layout {
        grid-template-columns: 1fr; /* Одна колонка на средних экранах */
        gap: 20px;
        max-width: 100%;
    }

    .characteristics-sections {
        flex-direction: column; /* Возвращаем вертикальное расположение на средних экранах */
        gap: 20px; /* Уменьшаем зазор */
    }

    .characteristics-section {
        padding: 1.5rem; /* Уменьшаем отступы */
    }

    .builds-and-notes-column {
        gap: 20px; /* Уменьшаем зазор между сборками и примечаниями */
    }
}

@media (max-width: 992px) {
    .tank-header {
        flex-direction: column;
        gap: 2rem;
    }

    .characteristics-main-layout {
        grid-template-columns: 1fr; /* Одна колонка на планшетах */
        gap: 20px;
    }

    .characteristics-sections {
        flex-direction: column; /* Возвращаем вертикальное расположение на планшетах */
        gap: 15px;
    }

    .tank-header .tank-header-right .equipment-slots {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 6px;
        position: absolute;
        bottom: 5px;
        right: 10px;
    }

    .tank-header .equipment-slot {
        width: 55px;
        height: 55px;
    }
}

@media (max-width: 768px) {
    .tank-characteristics-page {
        padding: 0.5rem;
        padding-bottom: 140px;
    }

    .tank-icon-large {
        width: 120px;
        height: 75px;
    }

    .tank-characteristics-page .tank-name {
        font-size: clamp(0.9rem, 2vw, 1.1rem);
    }

    .tank-header .tank-header-right .equipment-slots {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 6px;
        position: absolute;
        bottom: 5px;
        right: 10px;
    }

    .tank-header .equipment-slot {
        width: 50px;
        height: 50px;
    }

    .builds-table-container {
        overflow: hidden;
        max-width: 100%;
    }

    .builds-table {
        width: 100%;
    }

    /* КОМПАКТНЫЕ ПРИМЕЧАНИЯ НА МОБИЛЬНЫХ */
    .notes-content-compact {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .note-item-compact {
        padding: 6px 10px;
    }

    .note-text-compact {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .note-text-compact strong {
        font-size: 0.85rem;
    }

    .notes-section {
        padding: 12px;
        margin: 16px 0;
    }

    .notes-header {
        margin-bottom: 12px;
        padding-bottom: 8px;
    }

    .notes-title h3 {
        font-size: 1rem;
    }

    .notes-icon {
        width: 20px;
        height: 20px;
    }
}

@media (max-width: 480px) {
    .tank-name-with-badge {
        flex-wrap: wrap;
        gap: 8px;
    }

    .tank-characteristics-page .tank-name {
        font-size: clamp(0.85rem, 2.5vw, 1rem);
    }

    .tank-characteristics-page .tank-type-badge {
        font-size: 0.75rem;
        padding: 3px 6px;
    }

    .characteristics-section {
        padding: 1.2rem;
    }

    .section-header h2 {
        font-size: 1.1rem;
    }

    .characteristic-value {
        font-size: 0.95rem;
    }
}