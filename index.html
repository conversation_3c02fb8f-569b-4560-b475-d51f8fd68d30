<!DOCTYPE html>
<html lang="ru" data-theme="tank-theme" class="h-full">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Efficiency</title>
  <meta name="description" content="Сравнитель танков World of Tanks с подробными характеристиками и рекомендациями по сборкам">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="src/assets/styles/main.css">
  <link rel="icon" type="image/png" href="src/assets/images/web/pie-chart.png">
  <style>
    /* Начальное скрытие секций до загрузки JavaScript */
    #overview-section, #tank-list, #compare-section, #settings-section, #tank-characteristics-container {
      display: none;
      opacity: 0;
      visibility: hidden;
    }
  </style>
</head>

<body class="font-sans antialiased tracking-wide font-normal leading-normal overflow-x-hidden relative z-0 min-h-screen h-full transition-colors duration-300 ease-in-out p-5 bg-[radial-gradient(circle_at_20%_20%,_rgba(114,84,228,0.07)_0%,_transparent_60%),_radial-gradient(circle_at_80%_30%,_rgba(54,180,220,0.07)_0%,_transparent_60%),_radial-gradient(circle_at_60%_80%,_rgba(220,70,150,0.07)_0%,_transparent_60%),_radial-gradient(circle_at_10%_90%,_rgba(120,210,100,0.07)_0%,_transparent_60%)] dark:bg-none">
  <!-- This div handles the main dark theme gradient (replaces body::after) -->
  <div id="bg-gradient-layer" class="fixed inset-0 -z-20 pointer-events-none dark:bg-gm-dark-gradient"></div>

  <!-- This div handles the background image pattern (replaces body::before) -->
  <div id="bg-pattern-layer" class="fixed inset-0 -z-10 bg-cover bg-center bg-fixed opacity-10 bg-bg-pattern-light dark:bg-bg-pattern-dark"></div>
  <!-- Добавляем световые эффекты для создания ощущения парения -->
  <div class="glow-effect-top-right hidden"></div>
  <div class="glow-effect-bottom-left hidden"></div>
  <div class="glow-effect-center hidden"></div>

  <!-- Главный контейнер с боковой панелью и основным контентом -->
  <div class="min-h-screen w-full relative z-10 transition-all duration-300 ease-linear">
    <!-- Боковая панель в стиле Unity -->
    <div class="sidebar flex flex-col overflow-hidden bg-gradient-to-b from-[#201937] to-[#1a142d] backdrop-blur-lg backdrop-saturate-[1.8] border-r-[1.5px] border-white/[0.04] transition-all duration-200 ease-linear isolate transform-gpu will-change-transform" style="position: fixed; top: 0; left: 0; bottom: 0; width: 250px; z-index: 10;">
      <div class="sidebar-header-content">
      <svg class="sidebar-logo-icon" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 15h-6.5a2.5 2.5 0 1 1 0 -5h.5" /><path d="M15 12v6.5a2.5 2.5 0 1 1 -5 0v-.5" /><path d="M12 9h6.5a2.5 2.5 0 1 1 0 5h-.5" /><path d="M9 12v-6.5a2.5 2.5 0 0 1 5 0v.5" /></svg>

        <div class="ai-title-text">AI Efficiency</div>
      </div>

      <div class="sidebar-section">
        <div class="section-title">ANALYSIS</div>

        <div class="sidebar-menu-item" data-section="overview">
          <div class="menu-icon"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M14 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/></svg></div>
          <div class="menu-text">Overview</div>
        </div>

        <div class="sidebar-menu-item" data-section="vehicles">
          <div class="menu-icon"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v0a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z"/><path d="M6 12l1 -5h5l3 5"/><path d="M21 9l-7.8 0"/></svg></div>
          <div class="menu-text">Vehicles</div>
        </div>
        <!-- Flags section (visible only when Vehicles tab is active) -->
        <div id="flag-section" class="flag-section" style="display: none;">
          <div id="nation-filter" class="flag-list">
            <button class="filter-item flag-item" data-country="ussr"><img src="src/assets/images/flags/ussr.webp" alt="USSR" /><span class="flag-name">USSR</span></button>
            <button class="filter-item flag-item" data-country="germany"><img src="src/assets/images/flags/germany.webp" alt="Germany" /><span class="flag-name">Germany</span></button>
            <button class="filter-item flag-item" data-country="usa"><img src="src/assets/images/flags/usa.webp" alt="USA" /><span class="flag-name">USA</span></button>
            <button class="filter-item flag-item" data-country="france"><img src="src/assets/images/flags/france.webp" alt="France" /><span class="flag-name">France</span></button>
            <button class="filter-item flag-item" data-country="uk"><img src="src/assets/images/flags/uk.webp" alt="UK" /><span class="flag-name">UK</span></button>
            <button class="filter-item flag-item" data-country="czech"><img src="src/assets/images/flags/czech.webp" alt="Czech" /><span class="flag-name">Czech</span></button>
            <button class="filter-item flag-item" data-country="china"><img src="src/assets/images/flags/china.webp" alt="China" /><span class="flag-name">China</span></button>
            <button class="filter-item flag-item" data-country="japan"><img src="src/assets/images/flags/japan.webp" alt="Japan" /><span class="flag-name">Japan</span></button>
            <button class="filter-item flag-item" data-country="poland"><img src="src/assets/images/flags/poland.webp" alt="Poland" /><span class="flag-name">Poland</span></button>
            <button class="filter-item flag-item" data-country="sweden"><img src="src/assets/images/flags/sweden.webp" alt="Sweden" /><span class="flag-name">Sweden</span></button>
            <button class="filter-item flag-item" data-country="italy"><img src="src/assets/images/flags/italy.webp" alt="Italy" /><span class="flag-name">Italy</span></button>
            <button class="filter-item flag-item" data-country="international"><img src="src/assets/images/flags/intunion.webp" alt="Int'l Union" /><span class="flag-name">Int'l Union</span></button>
          </div>
          <div class="flag-divider"></div>
          <!-- Tank type filters -->
          <div id="type-filter" class="type-list">
            <button class="filter-item type-item" data-category="ЛТ"><img src="src/assets/images/upgrades/lightTank.png" alt="ЛТ" /><span class="type-name">ЛТ</span></button>
            <button class="filter-item type-item" data-category="СТ"><img src="src/assets/images/upgrades/mediumTank.png" alt="СТ" /><span class="type-name">СТ</span></button>
            <button class="filter-item type-item" data-category="ТТ"><img src="src/assets/images/upgrades/heavyTank.png" alt="ТТ" /><span class="type-name">ТТ</span></button>
            <button class="filter-item type-item" data-category="ПТ-САУ"><img src="src/assets/images/upgrades/AT-SPG.png" alt="ПТ-САУ" /><span class="type-name">ПТ-САУ</span></button>
            <button class="filter-item type-item" data-category="САУ"><img src="src/assets/images/upgrades/SPG.png" alt="САУ" /><span class="type-name">САУ</span></button>
          </div>
        </div>

        <div class="sidebar-menu-item" data-section="compare">
          <div class="menu-icon"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M6 6m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M18 18m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" /><path d="M11 6h5a2 2 0 0 1 2 2v8" /><path d="M14 9l-3 -3l3 -3" /><path d="M13 18h-5a2 2 0 0 1 -2 -2v-8" /><path d="M10 15l3 3l-3 3" /></svg></div>
          <div class="menu-text">Compare</div>
        </div>

        <div class="sidebar-menu-item" data-section="settings">
          <div class="menu-icon"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z" /><path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0" /></svg></div>
          <div class="menu-text">Settings</div>
        </div>
      </div>
    </div>

    <div class="main-content-container" role="main">
      <!-- Content starts directly without filter panel -->

      <!-- Секция обзора (Overview) -->
      <div id="overview-section" class="content-section hidden" style="display: none;">
        <div class="section-container glassmorphism">
          <h2 class="section-title">WoT Analytics: Обзор</h2>
          <div class="section-content">
            <p>Добро пожаловать в WoT Analytics - инструмент для анализа техники в World of Tanks.</p>
            <div class="stats-overview">
              <div class="stat-card">
                <h3>Всего танков</h3>
                <div class="stat-value">350+</div>
              </div>
              <div class="stat-card">
                <h3>Наций</h3>
                <div class="stat-value">12</div>
              </div>
              <div class="stat-card">
                <h3>Типов техники</h3>
                <div class="stat-value">5</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Список танков (Vehicles) -->
      <div id="tank-list" class="content-section" role="list" aria-label="Список танков"></div>

      <!-- Страница характеристик танка -->
      <div id="tank-characteristics-container" class="content-section tank-characteristics hidden" style="display: none;">
        <!-- Содержимое будет добавлено через JavaScript -->
      </div>

      <!-- Секция сравнения (Compare) -->
      <div id="compare-section" class="content-section hidden" style="display: none;">
        <div class="modern-compare">
          <h2 class="modern-compare-title">Сравнение техники</h2>

          <div class="compare-selection-area">
            <div class="compare-tank-slot" data-slot="1">
              <div class="tank-slot-header">
                <span class="slot-number">1</span>
                <span class="slot-label">Первый танк</span>
              </div>
              <div class="tank-slot-content">
                <div class="tank-placeholder">
                  <svg class="placeholder-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                  </svg>
                  <span class="placeholder-text">Выберите танк</span>
                </div>
              </div>
            </div>

            <div class="compare-vs-divider">
              <span class="vs-text">VS</span>
            </div>

            <div class="compare-tank-slot" data-slot="2">
              <div class="tank-slot-header">
                <span class="slot-number">2</span>
                <span class="slot-label">Второй танк</span>
              </div>
              <div class="tank-slot-content">
                <div class="tank-placeholder">
                  <svg class="placeholder-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/>
                  </svg>
                  <span class="placeholder-text">Выберите танк</span>
                </div>
              </div>
            </div>
          </div>

          <div class="compare-actions">
            <button id="compare-btn" class="modern-compare-button" disabled>
              <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              Сравнить характеристики
            </button>
            <button id="clear-compare-btn" class="modern-clear-button">
              <svg class="button-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
              Очистить
            </button>
          </div>

          <div id="compare-results" class="compare-results-area hidden">
            <div class="results-header">
              <h3 class="results-title">Результаты сравнения</h3>
            </div>
            <div class="results-content">
              <!-- Результаты сравнения будут добавлены через JavaScript -->
            </div>
          </div>
        </div>
      </div>

      <!-- Секция настроек (Settings) -->
      <div id="settings-section" class="content-section hidden" style="display: none;">
        <div class="modern-settings">
          <h2 class="modern-settings-title">Настройки</h2>

          <div class="modern-setting-row">
            <label class="modern-setting-label">Тема</label>
            <div class="custom-dropdown" data-dropdown="theme">
              <div class="dropdown-selected" data-value="dark">
                <span class="dropdown-text">Темная</span>
                <svg class="dropdown-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="dropdown-options">
                <div class="dropdown-option" data-value="dark">Темная</div>
                <div class="dropdown-option" data-value="light">Светлая</div>
              </div>
            </div>
          </div>

          <div class="modern-setting-row">
            <label class="modern-setting-label">Язык</label>
            <div class="custom-dropdown" data-dropdown="language">
              <div class="dropdown-selected" data-value="ru">
                <span class="dropdown-text">Русский</span>
                <svg class="dropdown-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="dropdown-options">
                <div class="dropdown-option" data-value="ru">Русский</div>
                <div class="dropdown-option" data-value="en">English</div>
              </div>
            </div>
          </div>

          <div class="modern-setting-row">
            <label class="modern-setting-label">Размер шрифта</label>
            <div class="custom-dropdown" data-dropdown="font-size">
              <div class="dropdown-selected" data-value="medium">
                <span class="dropdown-text">Средний</span>
                <svg class="dropdown-icon" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="dropdown-options">
                <div class="dropdown-option" data-value="small">Мелкий</div>
                <div class="dropdown-option" data-value="medium">Средний</div>
                <div class="dropdown-option" data-value="large">Крупный</div>
              </div>
            </div>
          </div>

          <div class="modern-setting-row">
            <label class="modern-setting-label">Кэширование</label>
            <div class="modern-toggle">
              <input type="checkbox" id="cache-toggle" checked>
              <span class="modern-toggle-slider"></span>
            </div>
          </div>

          <div class="modern-setting-row">
            <label class="modern-setting-label">Очистка данных</label>
            <button id="clear-cache-btn" class="modern-button">Очистить кэш</button>
          </div>
        </div>
      </div>









    </div> <!-- Конец .main-content-container -->
  </div> <!-- Конец .app-container -->

  <!-- Модальное окно в стиле Unity (скрыто по умолчанию) -->
  <div id="build-modal" class="modal hidden unity-modal" role="dialog" aria-modal="true" aria-labelledby="modal-title">
    <div id="modal-body" class="unity-modal-content">
      <!-- Контент будет добавлен через JavaScript -->
    </div>
  </div>

  <script type="module" src="src/main.js"></script>
  
  <!-- Оптимизированный скрипт для управления примечаниями -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Функция для инициализации состояния примечаний
      function initializeNotesState() {
        // Находим секцию примечаний
        const notesSection = document.querySelector('.notes-section');
        const toggleIcon = document.getElementById('toggle-notes-icon');

        if (notesSection && toggleIcon) {
          // По умолчанию примечания ПОКАЗАНЫ
          notesSection.classList.remove('notes-hidden');
          toggleIcon.style.transform = 'rotate(0deg)';
          toggleIcon.style.stroke = '#ff3b30';
        }
      }
      
      // Запускаем инициализацию при загрузке страницы
      initializeNotesState();
      
      // Также запускаем при изменении хэша (переход к другому танку)
      window.addEventListener('hashchange', function() {
        // Даем время для загрузки контента
        setTimeout(initializeNotesState, 500);
      });
    });
  </script>
</body>

</html>
