/**
 * Lazy Loading utilities for dynamic imports
 * Оптимизированная система ленивой загрузки модулей
 */

// Кэш загруженных модулей
const moduleCache = new Map();

// Кэш промисов загрузки (предотвращает дублирование запросов)
const loadingPromises = new Map();

/**
 * Ленивая загрузка модуля с кэшированием
 * @param {string} modulePath - Путь к модулю
 * @param {string} exportName - Имя экспорта (по умолчанию 'default')
 * @returns {Promise} Промис с загруженным модулем
 */
export async function lazyLoad(modulePath, exportName = 'default') {
  const cacheKey = `${modulePath}:${exportName}`;
  
  // Проверяем кэш
  if (moduleCache.has(cacheKey)) {
    return moduleCache.get(cacheKey);
  }
  
  // Проверяем, не загружается ли уже этот модуль
  if (loadingPromises.has(cacheKey)) {
    return loadingPromises.get(cacheKey);
  }
  
  // Создаем промис загрузки
  const loadingPromise = import(modulePath)
    .then(module => {
      const exportedValue = exportName === 'default' ? module.default : module[exportName];
      moduleCache.set(cacheKey, exportedValue);
      loadingPromises.delete(cacheKey);
      return exportedValue;
    })
    .catch(error => {
      loadingPromises.delete(cacheKey);
      console.error(`Failed to load module ${modulePath}:`, error);
      throw error;
    });
  
  loadingPromises.set(cacheKey, loadingPromise);
  return loadingPromise;
}

/**
 * Предзагрузка модуля (без выполнения)
 * @param {string} modulePath - Путь к модулю
 */
export function preloadModule(modulePath) {
  if (typeof document !== 'undefined') {
    const link = document.createElement('link');
    link.rel = 'modulepreload';
    link.href = modulePath;
    document.head.appendChild(link);
  }
}

/**
 * Ленивая загрузка компонента с fallback
 * @param {string} componentPath - Путь к компоненту
 * @param {Function} fallback - Функция fallback при ошибке
 * @returns {Promise} Промис с компонентом
 */
export async function lazyLoadComponent(componentPath, fallback = null) {
  try {
    return await lazyLoad(componentPath);
  } catch (error) {
    console.warn(`Component ${componentPath} failed to load, using fallback:`, error);
    return fallback || (() => {
      console.error(`No fallback provided for ${componentPath}`);
      return null;
    });
  }
}

/**
 * Батчевая предзагрузка модулей
 * @param {string[]} modulePaths - Массив путей к модулям
 */
export function preloadModules(modulePaths) {
  modulePaths.forEach(path => {
    // Используем requestIdleCallback если доступен
    if (typeof requestIdleCallback !== 'undefined') {
      requestIdleCallback(() => preloadModule(path));
    } else {
      setTimeout(() => preloadModule(path), 0);
    }
  });
}

/**
 * Очистка кэша модулей
 * @param {string} pattern - Паттерн для очистки (опционально)
 */
export function clearModuleCache(pattern = null) {
  if (pattern) {
    const regex = new RegExp(pattern);
    for (const [key] of moduleCache) {
      if (regex.test(key)) {
        moduleCache.delete(key);
      }
    }
  } else {
    moduleCache.clear();
  }
}

/**
 * Получение статистики кэша
 * @returns {Object} Статистика кэша
 */
export function getCacheStats() {
  return {
    cachedModules: moduleCache.size,
    loadingModules: loadingPromises.size,
    cacheKeys: Array.from(moduleCache.keys())
  };
}

/**
 * Ленивая загрузка с retry логикой
 * @param {string} modulePath - Путь к модулю
 * @param {number} maxRetries - Максимальное количество попыток
 * @param {number} delay - Задержка между попытками (мс)
 * @returns {Promise} Промис с модулем
 */
export async function lazyLoadWithRetry(modulePath, maxRetries = 3, delay = 1000) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await lazyLoad(modulePath);
    } catch (error) {
      lastError = error;
      console.warn(`Attempt ${attempt}/${maxRetries} failed for ${modulePath}:`, error);
      
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt));
      }
    }
  }
  
  throw new Error(`Failed to load ${modulePath} after ${maxRetries} attempts: ${lastError.message}`);
}

/**
 * Условная ленивая загрузка
 * @param {Function} condition - Функция условия
 * @param {string} modulePath - Путь к модулю
 * @param {string} exportName - Имя экспорта
 * @returns {Promise|null} Промис с модулем или null
 */
export async function conditionalLazyLoad(condition, modulePath, exportName = 'default') {
  if (typeof condition === 'function' ? condition() : condition) {
    return lazyLoad(modulePath, exportName);
  }
  return null;
}

/**
 * Ленивая загрузка с таймаутом
 * @param {string} modulePath - Путь к модулю
 * @param {number} timeout - Таймаут в миллисекундах
 * @param {string} exportName - Имя экспорта
 * @returns {Promise} Промис с модулем
 */
export async function lazyLoadWithTimeout(modulePath, timeout = 10000, exportName = 'default') {
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`Timeout loading ${modulePath}`)), timeout);
  });
  
  return Promise.race([
    lazyLoad(modulePath, exportName),
    timeoutPromise
  ]);
}

/**
 * Инициализация системы ленивой загрузки
 */
export function initLazyLoader() {
  console.log('🔄 Lazy loader initialized');
  
  // Предзагружаем критические модули
  const criticalModules = [
    './components/tank-details/TankDetails.js',
    './components/tank-list/TankList.js',
    './services/FilterService.js'
  ];
  
  preloadModules(criticalModules);
  
  // Логируем статистику в dev режиме
  if (import.meta.env.DEV) {
    setInterval(() => {
      const stats = getCacheStats();
      if (stats.cachedModules > 0) {
        console.log('📊 Lazy loader stats:', stats);
      }
    }, 30000);
  }
}

// Экспорт для глобального использования
if (typeof window !== 'undefined') {
  window.lazyLoad = lazyLoad;
  window.lazyLoaderStats = getCacheStats;
}
