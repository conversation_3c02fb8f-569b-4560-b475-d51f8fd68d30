// Экспорт всех компонентов деталей танка
export { 
  initializeTankDetails,
  updateTankDetailsUI,
  updateTankHeader,
  showTankDetails,
  hideTankDetails,
  setupTooltips
} from './TankDetails.js';

export {
  updateCharacteristics,
  updateCharacteristicsWithEquipment,
  createCharacteristicTable,
  hideAllProgressBars,
  updateProgressBar,
  calculatePercentage
} from './CharacteristicsTable.js';

// Глобальная функция для обновления характеристик (для обратной совместимости)
window.updateCharacteristics = function(characteristics) {
  import('./CharacteristicsTable.js').then(module => {
    module.updateCharacteristics(characteristics);
  });
};

// Глобальная функция для обновления характеристик с учетом оборудования
window.updateCharacteristicsWithEquipment = function() {
  import('./CharacteristicsTable.js').then(module => {
    import('../../store/state.js').then(stateModule => {
      const state = stateModule.state;
      if (state.selectedTank && state.selectedTank.characteristics) {
        module.updateCharacteristicsWithEquipment(
          state.selectedTank.characteristics,
          state.selectedEquipment
        );
      }
    });
  });
};
