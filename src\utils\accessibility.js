/**
 * Accessibility utilities
 * Утилиты для улучшения доступности
 */

// Менеджер фокуса
class FocusManager {
  constructor() {
    this.focusStack = [];
    this.trapStack = [];
    this.setupGlobalListeners();
  }

  /**
   * Установка глобальных слушателей
   */
  setupGlobalListeners() {
    // Обработка клавиши Tab для навигации
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        this.handleTabNavigation(e);
      }
      
      // Escape для закрытия модальных окон
      if (e.key === 'Escape') {
        this.handleEscapeKey(e);
      }
    });

    // Обработка клика для управления фокусом
    document.addEventListener('click', (e) => {
      this.handleClick(e);
    });
  }

  /**
   * Сохранение текущего фокуса
   */
  saveFocus() {
    const activeElement = document.activeElement;
    this.focusStack.push(activeElement);
    return activeElement;
  }

  /**
   * Восстановление фокуса
   */
  restoreFocus() {
    const element = this.focusStack.pop();
    if (element && typeof element.focus === 'function') {
      element.focus();
    }
  }

  /**
   * Установка фокуса на элемент
   * @param {Element} element - Элемент для фокуса
   * @param {Object} options - Опции фокуса
   */
  setFocus(element, options = {}) {
    if (!element) return;

    const { preventScroll = false, selectText = false } = options;

    element.focus({ preventScroll });

    if (selectText && element.select) {
      element.select();
    }
  }

  /**
   * Ловушка фокуса в контейнере
   * @param {Element} container - Контейнер для ловушки
   */
  trapFocus(container) {
    if (!container) return;

    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const trapHandler = (e) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', trapHandler);
    this.trapStack.push({ container, handler: trapHandler });

    // Устанавливаем фокус на первый элемент
    firstElement.focus();
  }

  /**
   * Освобождение ловушки фокуса
   */
  releaseFocusTrap() {
    const trap = this.trapStack.pop();
    if (trap) {
      trap.container.removeEventListener('keydown', trap.handler);
    }
  }

  /**
   * Получение фокусируемых элементов
   * @param {Element} container - Контейнер для поиска
   * @returns {Element[]} Массив фокусируемых элементов
   */
  getFocusableElements(container = document) {
    const selector = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]'
    ].join(', ');

    return Array.from(container.querySelectorAll(selector))
      .filter(element => {
        return element.offsetWidth > 0 && 
               element.offsetHeight > 0 && 
               !element.hidden;
      });
  }

  /**
   * Обработка навигации Tab
   * @param {KeyboardEvent} e - Событие клавиатуры
   */
  handleTabNavigation(e) {
    // Логика для улучшенной навигации
    const focusableElements = this.getFocusableElements();
    const currentIndex = focusableElements.indexOf(document.activeElement);
    
    // Добавляем визуальные индикаторы фокуса
    this.addFocusIndicator(document.activeElement);
  }

  /**
   * Обработка клавиши Escape
   * @param {KeyboardEvent} e - Событие клавиатуры
   */
  handleEscapeKey(e) {
    // Закрытие модальных окон, меню и т.д.
    const modal = document.querySelector('[role="dialog"][aria-modal="true"]');
    if (modal) {
      this.closeModal(modal);
    }
  }

  /**
   * Обработка клика
   * @param {MouseEvent} e - Событие мыши
   */
  handleClick(e) {
    // Убираем индикаторы фокуса при клике мышью
    this.removeFocusIndicators();
  }

  /**
   * Добавление визуального индикатора фокуса
   * @param {Element} element - Элемент
   */
  addFocusIndicator(element) {
    if (!element) return;
    
    element.classList.add('focus-visible');
  }

  /**
   * Удаление всех индикаторов фокуса
   */
  removeFocusIndicators() {
    document.querySelectorAll('.focus-visible').forEach(element => {
      element.classList.remove('focus-visible');
    });
  }
}

// Глобальный экземпляр менеджера фокуса
export const focusManager = new FocusManager();

/**
 * Утилиты для ARIA
 */
export class AriaUtils {
  /**
   * Установка ARIA атрибутов
   * @param {Element} element - Элемент
   * @param {Object} attributes - ARIA атрибуты
   */
  static setAttributes(element, attributes) {
    if (!element) return;

    Object.entries(attributes).forEach(([key, value]) => {
      if (key.startsWith('aria-') || key === 'role') {
        element.setAttribute(key, value);
      }
    });
  }

  /**
   * Объявление для скрин-ридеров
   * @param {string} message - Сообщение
   * @param {string} priority - Приоритет ('polite' | 'assertive')
   */
  static announce(message, priority = 'polite') {
    const announcer = this.getAnnouncer(priority);
    announcer.textContent = message;

    // Очищаем через небольшую задержку
    setTimeout(() => {
      announcer.textContent = '';
    }, 1000);
  }

  /**
   * Получение элемента для объявлений
   * @param {string} priority - Приоритет
   * @returns {Element} Элемент announcer
   */
  static getAnnouncer(priority) {
    const id = `aria-announcer-${priority}`;
    let announcer = document.getElementById(id);

    if (!announcer) {
      announcer = document.createElement('div');
      announcer.id = id;
      announcer.setAttribute('aria-live', priority);
      announcer.setAttribute('aria-atomic', 'true');
      announcer.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `;
      document.body.appendChild(announcer);
    }

    return announcer;
  }

  /**
   * Создание описания для элемента
   * @param {Element} element - Элемент
   * @param {string} description - Описание
   */
  static addDescription(element, description) {
    if (!element) return;

    const descId = `desc-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const descElement = document.createElement('div');
    descElement.id = descId;
    descElement.textContent = description;
    descElement.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `;

    document.body.appendChild(descElement);
    element.setAttribute('aria-describedby', descId);
  }
}

/**
 * Утилиты для клавиатурной навигации
 */
export class KeyboardUtils {
  /**
   * Добавление поддержки клавиатурных сокращений
   * @param {Object} shortcuts - Объект с сокращениями
   */
  static addShortcuts(shortcuts) {
    document.addEventListener('keydown', (e) => {
      const key = this.getKeyCombo(e);
      const handler = shortcuts[key];
      
      if (handler && typeof handler === 'function') {
        e.preventDefault();
        handler(e);
      }
    });
  }

  /**
   * Получение комбинации клавиш
   * @param {KeyboardEvent} e - Событие клавиатуры
   * @returns {string} Строка комбинации
   */
  static getKeyCombo(e) {
    const parts = [];
    
    if (e.ctrlKey) parts.push('ctrl');
    if (e.altKey) parts.push('alt');
    if (e.shiftKey) parts.push('shift');
    if (e.metaKey) parts.push('meta');
    
    parts.push(e.key.toLowerCase());
    
    return parts.join('+');
  }

  /**
   * Создание skip link
   * @param {string} targetId - ID целевого элемента
   * @param {string} text - Текст ссылки
   */
  static createSkipLink(targetId, text = 'Skip to main content') {
    const skipLink = document.createElement('a');
    skipLink.href = `#${targetId}`;
    skipLink.textContent = text;
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
      position: absolute;
      top: -40px;
      left: 6px;
      background: #000;
      color: #fff;
      padding: 8px;
      text-decoration: none;
      z-index: 10000;
      transition: top 0.3s;
    `;

    skipLink.addEventListener('focus', () => {
      skipLink.style.top = '6px';
    });

    skipLink.addEventListener('blur', () => {
      skipLink.style.top = '-40px';
    });

    document.body.insertBefore(skipLink, document.body.firstChild);
  }
}

/**
 * Инициализация системы доступности
 */
export function initAccessibility() {
  console.log('♿ Accessibility system initialized');

  // Создаем skip links
  KeyboardUtils.createSkipLink('main-content', 'Перейти к основному содержимому');

  // Добавляем базовые клавиатурные сокращения
  KeyboardUtils.addShortcuts({
    'alt+1': () => focusManager.setFocus(document.querySelector('h1')),
    'alt+2': () => focusManager.setFocus(document.querySelector('#tank-search')),
    'alt+3': () => focusManager.setFocus(document.querySelector('#tank-list')),
    'ctrl+/': () => AriaUtils.announce('Доступные сокращения: Alt+1 - заголовок, Alt+2 - поиск, Alt+3 - список танков')
  });

  // Добавляем CSS для индикаторов фокуса
  const style = document.createElement('style');
  style.textContent = `
    .focus-visible {
      outline: 2px solid #8b5cf6 !important;
      outline-offset: 2px !important;
    }
    
    .skip-link:focus {
      outline: 2px solid #fff !important;
    }
  `;
  document.head.appendChild(style);

  // Инициализируем ARIA live regions
  AriaUtils.getAnnouncer('polite');
  AriaUtils.getAnnouncer('assertive');
}

// Экспорт для глобального использования
if (typeof window !== 'undefined') {
  window.focusManager = focusManager;
  window.AriaUtils = AriaUtils;
  window.KeyboardUtils = KeyboardUtils;
}
