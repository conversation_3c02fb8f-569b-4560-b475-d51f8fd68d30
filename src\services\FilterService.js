// src/filters.js
// Centralised filter utilities used across the application

// Map Russian abbreviations to internal tank type identifiers used in data structures
export const RUSSIAN_TO_INTERNAL_TYPE_MAP = {
  'ЛТ': 'lightTank',
  'СТ': 'mediumTank',
  'ТТ': 'heavyTank',
  'ПТ-САУ': 'at-spg',
  'САУ': 'spg'
};

// Inverse mapping for UI display purposes
export const INTERNAL_TO_RUSSIAN_TYPE_MAP = Object.fromEntries(
  Object.entries(RUSSIAN_TO_INTERNAL_TYPE_MAP).map(([rus, internal]) => [internal, rus])
);

// Display order for Russian tank type abbreviations
export const TANK_TYPE_ORDER = {
  'ЛТ': 1,
  'СТ': 2,
  'ТТ': 3,
  'ПТ-САУ': 4,
  'САУ': 5
};

// Legacy English mapping for backward compatibility
export const ENGLISH_TO_INTERNAL_TYPE_MAP = {
  'LT': 'lightTank',
  'MT': 'mediumTank',
  'HT': 'heavyTank',
  'TD': 'at-spg',
  'SPG': 'spg'
};

export const INTERNAL_TO_ENGLISH_TYPE_MAP = Object.fromEntries(
  Object.entries(ENGLISH_TO_INTERNAL_TYPE_MAP).map(([eng, internal]) => [internal, eng])
);

// Map alternative / UI country identifiers to canonical keys used in data.js
export const COUNTRY_ALIASES = {
  // Accept various spellings but canonical key is "international"
  'international': 'international',
  'intunion': 'international', // legacy spelling in older data
  "int'l union": 'international'
};

function normalizeCountry(value) {
  if (!value) return value;
  const lower = value.toLowerCase();
  return COUNTRY_ALIASES[lower] || lower;
}

/**
 * Check if a tank satisfies the active country and category filters.
 *
 * @param {Object} tank                Tank object from state.allTanks
 * @param {Object} filters             { country: string, type: string }
 * @returns {boolean}                  True if the tank matches all filters
 */
export function matchesFilters(tank, filters) {
  // Compare countries using normalized aliases to bridge UI values (e.g., "international")
  // with dataset keys (e.g., "International") and ignore case.
  const filterCountry = normalizeCountry(filters.country);
  const tankCountry = normalizeCountry(tank.country);

  if (filterCountry && filterCountry !== 'all' && tankCountry !== filterCountry) {
    return false;
  }

  // Category / type filter – compare internal identifiers
  const tankInternalType = RUSSIAN_TO_INTERNAL_TYPE_MAP[tank.type] || ENGLISH_TO_INTERNAL_TYPE_MAP[tank.type];
  if (filters.type && filters.type !== 'all' && tankInternalType !== filters.type) {
    return false;
  }

  return true;
}

/**
 * Produce a sorted list of tanks that satisfy the current filters and search query.
 *
 * @param {Object} state         Global reactive state object
 * @param {TankSearchIndex} searchIndex  Trie-based search index instance
 * @returns {Array<Object>}      Array of tank objects ready for rendering
 */
export function getFilteredTanks(state, searchIndex) {
  const { selectedCountry, selectedCategory, searchQuery, allTanks } = state;

  let filtered;

  if (searchQuery) {
    // Get tanks whose names match the query first, then apply filters
    const searchResults = searchIndex ? Array.from(searchIndex.search(searchQuery)) : [];
    filtered = searchResults.filter(tank =>
      matchesFilters(tank, { country: selectedCountry, type: selectedCategory })
    );
  } else {
    // No search term – simply filter all tanks
    filtered = allTanks.filter(tank =>
      matchesFilters(tank, { country: selectedCountry, type: selectedCategory })
    );
  }

  // Sort primarily by type (Russian abbreviation order) and secondarily by name
  filtered.sort((a, b) => {
    const orderA = TANK_TYPE_ORDER[a.type] ?? 999;
    const orderB = TANK_TYPE_ORDER[b.type] ?? 999;

    if (orderA !== orderB) return orderA - orderB;
    return a.name.localeCompare(b.name);
  });

  return filtered;
}
