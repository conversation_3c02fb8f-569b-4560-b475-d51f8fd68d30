// Модуль для анализа танков и определения оптимальных диапазонов характеристик
// Этот модуль будет автоматически адаптировать диапазоны при добавлении новых танков

import { TANK_TYPES } from '../data/tanks.js';
// Removed unused imports: TANK_ROLES, tanksData as initialTanksData, TANK_LEVEL
import { STANDARD_EQUIPMENT, SPECIAL_EQUIPMENT, IMPROVED_EQUIPMENT, EXPERIMENTAL_EQUIPMENT } from '../data/equipment.js';

let tanksCache = null;
let characteristicRanges = null;

// Базовые диапазоны, используемые в случае отсутствия данных
const DEFAULT_RANGES = {
  // Основные характеристики вооружения
  dpm: { min: 1800, max: 3800, inverse: false },
  damage: { min: 320, max: 750, inverse: false },
  penetration: { min: 200, max: 340, inverse: false },
  reloadTime: { min: 20, max: 5, inverse: true },
  aimTime: { min: 4.0, max: 1.5, inverse: true },
  dispersion: { min: 0.5, max: 0.25, inverse: true },
  
  // Новые характеристики вооружения
  penetration500m: { min: 180, max: 320, inverse: false },
  dispersionDamage: { min: 0.9, max: 0.2, inverse: true },
  moveDispersion: { min: 0.25, max: 0.08, inverse: true },
  hullTraverseDispersion: { min: 0.25, max: 0.05, inverse: true },
  turretTraverseDispersion: { min: 0.15, max: 0.02, inverse: true },
  afterShotDispersion: { min: 5, max: 2, inverse: true },
  dispersionFactor: { min: 3, max: 1, inverse: true },
  modulesDamage: { min: 50, max: 300, inverse: false },
  caliber: { min: 50, max: 150, inverse: false },
  shellVelocity: { min: 800, max: 1600, inverse: false },
  maxRange: { min: 500, max: 800, inverse: false },
  ammoCapacity: { min: 30, max: 60, inverse: false },
  potentialDamage: { min: 10000, max: 30000, inverse: false },
  shellCost: { min: 400, max: 2000, inverse: true },
  rateOfFire: { min: 4, max: 15, inverse: false },
  
  // Характеристики подвижности
  speed: { min: 30, max: 70, inverse: false },
  reverseSpeed: { min: 10, max: 30, inverse: false },
  enginePower: { min: 400, max: 1500, inverse: false },
  powerToWeight: { min: 8, max: 30, inverse: false },
  traverse: { min: 20, max: 60, inverse: false },
  
  // Прочие характеристики
  viewRange: { min: 350, max: 450, inverse: false },
  hitPoints: { min: 1200, max: 3550, inverse: false },
  radioRange: { min: 500, max: 850, inverse: false },
  fireChance: { min: 0, max: 20, inverse: true },
  hullFront: { min: 60, max: 300, inverse: false },
  turretFront: { min: 100, max: 350, inverse: false }
};

// Загрузка данных о танках
const TANK_TYPE_SORT_ORDER = {
  'ЛТ': 1,
  'СТ': 2,
  'ТТ': 3,
  'ПТ-САУ': 4,
  'САУ': 5
};

async function loadTanksData() {
  if (tanksCache !== null) {
    return tanksCache;
  }
  
  try {
    // Вместо загрузки из Tanks.json используем данные из data.js
    if (window.tanksData) {
      const flattenedTanks = [];
      Object.entries(window.tanksData).forEach(([country, types]) => {
        Object.entries(types).forEach(([type, tanks]) => {
          tanks.forEach(tank => {
            flattenedTanks.push({
              ...tank,
              country,
              type
            });
          });
        });
      });

      // Sort the flattened tanks
      flattenedTanks.sort((a, b) => {
        const typeOrderA = TANK_TYPE_SORT_ORDER[a.type] || 99; // Default for unknown types
        const typeOrderB = TANK_TYPE_SORT_ORDER[b.type] || 99;

        if (typeOrderA !== typeOrderB) {
          return typeOrderA - typeOrderB;
        }
        // If types are the same, sort by name
        return a.name.localeCompare(b.name);
      });
      
      tanksCache = flattenedTanks;
      console.log(`Loaded and sorted ${tanksCache.length} tanks from data.js`);
      return tanksCache;
    } else {
      console.warn('tanksData not found in global scope, using empty array');
      tanksCache = [];
      return [];
    }
  } catch (error) {
    console.error('Error loading and sorting tanks data:', error);
    tanksCache = [];
    return [];
  }
}

// Анализ всех танков и определение оптимальных диапазонов для каждой характеристики
async function analyzeCharacteristicRanges() {
  if (characteristicRanges !== null) {
    return characteristicRanges;
  }
  
  const tanks = await loadTanksData();
  if (tanks.length === 0) {
    console.warn('No tanks data available, using default ranges');
    return DEFAULT_RANGES;
  }
  
  // Инициализация статистики
  const stats = {};
  Object.keys(DEFAULT_RANGES).forEach(key => {
    stats[key] = { min: Number.MAX_VALUE, max: Number.MIN_VALUE, count: 0 };
  });
  
  // Сбор данных по всем танкам
  tanks.forEach(tank => {
    if (tank.characteristics) {
      // Обработка простых характеристик
      Object.keys(tank.characteristics).forEach(key => {
        const value = tank.characteristics[key];
        // Обрабатываем только числовые характеристики, которые нас интересуют
        if (typeof value === 'number' && stats[key]) {
          stats[key].min = Math.min(stats[key].min, value);
          stats[key].max = Math.max(stats[key].max, value);
          stats[key].count++;
        }
      });
      
      // Обработка вложенных характеристик (например, броня)
      if (tank.characteristics.hullArmor && tank.characteristics.hullArmor.front && stats.hullFront) {
        const value = tank.characteristics.hullArmor.front;
        stats.hullFront.min = Math.min(stats.hullFront.min, value);
        stats.hullFront.max = Math.max(stats.hullFront.max, value);
        stats.hullFront.count++;
      }
      
      if (tank.characteristics.turretArmor && tank.characteristics.turretArmor.front && stats.turretFront) {
        const value = tank.characteristics.turretArmor.front;
        stats.turretFront.min = Math.min(stats.turretFront.min, value);
        stats.turretFront.max = Math.max(stats.turretFront.max, value);
        stats.turretFront.count++;
      }
    }
  });
  
  // Формирование адаптивных диапазонов
  const adaptiveRanges = {...DEFAULT_RANGES};
  
  Object.keys(stats).forEach(key => {
    // Проверяем, что у нас есть достаточно данных для характеристики
    if (stats[key].count > 0 && stats[key].min !== Number.MAX_VALUE && stats[key].max !== Number.MIN_VALUE) {
      // Для нормализованной оценки мы расширяем диапазон чуть больше,
      // чтобы не было слишком много максимальных или минимальных значений
      const range = stats[key].max - stats[key].min;
      
      // Используем разные отступы для минимума и максимума
      // Для инвертированных значений логика обратная
      let minPadding, maxPadding;
      const isInverse = DEFAULT_RANGES[key]?.inverse || false;
      
      if (isInverse) {
        // Для инвертированных значений (меньше = лучше)
        // Расширяем больше в сторону плохих значений (высоких)
        minPadding = range * 0.05; // 5% отступ в лучшую сторону
        maxPadding = range * 0.15; // 15% отступ в худшую сторону
      } else {
        // Для обычных значений (больше = лучше)
        // Расширяем больше в сторону плохих значений (низких)
        minPadding = range * 0.15; // 15% отступ в худшую сторону
        maxPadding = range * 0.05; // 5% отступ в лучшую сторону
      }
      
      adaptiveRanges[key] = {
        min: Math.max(0, stats[key].min - minPadding),
        max: stats[key].max + maxPadding,
        inverse: isInverse
      };
      
      console.log(`Analyzed ${key}: min=${adaptiveRanges[key].min.toFixed(2)}, max=${adaptiveRanges[key].max.toFixed(2)}, inverse=${adaptiveRanges[key].inverse}, from ${stats[key].count} tanks`);
    }
  });
  
  characteristicRanges = adaptiveRanges;
  return adaptiveRanges;
}

// Получение нормализованного процента для характеристики
function getNormalizedPercentage(value, characteristic) {
  // Неверные или пустые значения → 0
  if (typeof value !== 'number' || Number.isNaN(value)) {
    return 0;
  }

  const ranges = characteristicRanges || DEFAULT_RANGES;
  const range = ranges[characteristic];

  // Если диапазон неизвестен — возвращаем 50 % (середина шкалы)
  if (!range) return 50;

  // Гарантируем, что min < max независимо от того, как они заданы
  const min = Math.min(range.min ?? 0, range.max ?? 100);
  const max = Math.max(range.min ?? 0, range.max ?? 100);

  if (max === min) return 50; // избегаем деления на 0

  let pct = ((value - min) / (max - min)) * 100;
  pct = Math.max(0, Math.min(100, pct));

  if (range.inverse) {
    pct = 100 - pct;
  }

  return pct;
}

// Рассчитывает общий рейтинг танка на основе его характеристик
function calculateTankRating(characteristics) {
  if (!characteristics) {
    return 0;
  }
  
  // Веса различных характеристик для формирования общей оценки
  const weights = {
    dpm: 1.5,
    damage: 1.0,
    penetration: 1.3,
    aimTime: 1.2,
    dispersion: 1.2,
    reloadTime: 1.0,
    hitPoints: 1.0,
    viewRange: 1.1,
    speed: 1.0,
    powerToWeight: 0.9,
    turretFront: 1.0,
    hullFront: 0.8
  };
  
  let totalWeight = 0;
  let weightedSum = 0;
  
  // Суммируем нормализованные значения с учетом весов
  Object.keys(weights).forEach(key => {
    let value = characteristics[key];
    
    // Обработка nested-объектов
    if (key === 'hullFront' && characteristics.hullArmor) {
      value = characteristics.hullArmor.front;
    } else if (key === 'turretFront' && characteristics.turretArmor) {
      value = characteristics.turretArmor.front;
    }
    
    if (value !== undefined && value !== null) {
      const percentage = getNormalizedPercentage(value, key);
      weightedSum += percentage * weights[key];
      totalWeight += weights[key];
    }
  });
  
  return totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 0;
}

// Сравнивает две характеристики танка (базовую и с модификациями) и возвращает процент изменения
function compareCharacteristic(baseValue, modifiedValue, characteristicKey) {
  if (baseValue === undefined || modifiedValue === undefined) {
    return { percent: 0, improvement: false };
  }
  
  const range = characteristicRanges?.[characteristicKey] || DEFAULT_RANGES[characteristicKey];
  const inverse = range?.inverse || false;
  
  // Процентное изменение
  const percentChange = ((modifiedValue - baseValue) / baseValue) * 100;
  
  // Определяем, является ли изменение улучшением
  const improvement = inverse ? percentChange < 0 : percentChange > 0;
  
  return {
    percent: Math.abs(percentChange),
    improvement
  };
}

// Helper to deep clone an object
const deepClone = (obj) => JSON.parse(JSON.stringify(obj));

// Function to map tank type to camouflage bonus key
function getCamouflageTypeKey(tankType) {
    switch (tankType) {
        case TANK_TYPES.LIGHT:
        case TANK_TYPES.MEDIUM:
            return 'light_medium';
        case TANK_TYPES.HEAVY:
        case TANK_TYPES.SPG: // Assuming SPG falls under heavy_artillery for camo net
            return 'heavy_artillery';
        case TANK_TYPES.DESTROYER:
            return 'td';
        default:
            return 'light_medium'; // Default fallback
    }
}

function applyBonusesToTankStats(baseCharacteristics, activeEquipmentNames = [], tankRole, tankType) {
    if (!baseCharacteristics) return null;

    const modifiedStats = deepClone(baseCharacteristics);
    const allEquipment = {
        // Merge order matters: later keys override earlier ones.
        // We place STANDARD_EQUIPMENT last so its definitions win when names overlap,
        // ensuring tests that reference the standard variant get the expected stats.
        ...EXPERIMENTAL_EQUIPMENT,
        ...IMPROVED_EQUIPMENT,
        ...SPECIAL_EQUIPMENT,
        ...STANDARD_EQUIPMENT
    };

    let totalCrewBonusFactor = 0; // For things like Ventilation

    activeEquipmentNames.forEach(equipName => {
        const equipment = allEquipment[equipName];
        if (!equipment || !equipment.bonuses) return;

        // For now, simplifying to use 'base' bonuses. Category-specific slot logic would be more complex.
        // And special/improved might have 'upgrade' structure or direct bonuses.
        let bonusesToApply = {};
        if (equipment.type === 'standard' || equipment.type === 'experimental') {
            bonusesToApply = equipment.bonuses.base || equipment.bonuses; // Experimental might not have 'base'
        } else if (equipment.type === 'special' || equipment.type === 'improved') {
            // These might have a direct bonuses obj or an 'upgrade' obj for enhanced state
            // Assuming we use the upgraded version if 'upgrade.bonuses' exists, else direct 'bonuses'
            bonusesToApply = (equipment.upgrade && equipment.upgrade.bonuses) ? equipment.upgrade.bonuses : equipment.bonuses;
        }

        // Helper to apply a single bonus value to the modifiedStats object
        const applyBonus = (key, value) => {
            if (key === 'crewBonus') {
                totalCrewBonusFactor += value;
            } else if (key === 'camouflage') {
                if (typeof value === 'object' && modifiedStats.camouflage) {
                    const camoKey = getCamouflageTypeKey(tankType);
                    const camoVal = value[camoKey];
                    if (typeof camoVal === 'number') {
                        modifiedStats.camouflage.still  = (modifiedStats.camouflage.still  || 0) + camoVal;
                        modifiedStats.camouflage.moving = (modifiedStats.camouflage.moving || 0) + camoVal;
                    }
                }
            } else if (['viewRange','reloadTime','aimTime','dispersion','enginePower','hullDurability'].includes(key)) {
                if (typeof modifiedStats[key] === 'number' && typeof value === 'number') {
                    modifiedStats[key] *= (1 + value);
                }
            } else if (['speedForward','speedReverse','enemyDetectTime','ownDetectTime'].includes(key)) {
                if (typeof modifiedStats[key] === 'number' && typeof value === 'number') {
                    modifiedStats[key] += value;
                }
            } else if (typeof value === 'number' && typeof modifiedStats[key] === 'number') {
                // Fallback additive for any other numeric stat
                modifiedStats[key] += value;
            }
        };

        // Recursively traverse a bonus object that may contain nested "base" / "category" keys
        const traverseBonuses = (obj) => {
            for (const k in obj) {
                const v = obj[k];
                // Handle camouflage specially to preserve context (need full object)
                if (k === 'camouflage') {
                    applyBonus('camouflage', v);
                    continue;
                }

                if (v && typeof v === 'object' && !Array.isArray(v)) {
                    traverseBonuses(v);
                } else {
                    applyBonus(k, v);
                }
            }
        };

        // Apply all bonuses for this equipment, including nested ones
        traverseBonuses(bonusesToApply);
    });

    // Apply accumulated crewBonus (e.g., from Ventilation)
    // This typically affects: reloadTime, aimTime, dispersion, viewRange, (driver skills -> mobility)
    // The +5% from Bia/Vents is to the crew's main qualification level, which then non-linearly improves many tank stats.
    // A common simplification is applying a direct % bonus to related stats.
    // For reloadTime, aimTime, dispersion: a positive crewBonusFactor means they get *better* (smaller values).
    // For viewRange: a positive crewBonusFactor means it gets *better* (larger value).
    if (totalCrewBonusFactor > 0) {
        const crewImpactFactor = 1 + totalCrewBonusFactor; // e.g., 1.05 for +5%
        const inverseCrewImpactFactor = 1 / (1 + totalCrewBonusFactor); // e.g., 1 / 1.05 for reduction

        if (modifiedStats.reloadTime) modifiedStats.reloadTime *= inverseCrewImpactFactor;
        if (modifiedStats.aimTime) modifiedStats.aimTime *= inverseCrewImpactFactor;
        // Dispersion is complex: gunner's skill affects it. A simple model:
        if (modifiedStats.dispersion) modifiedStats.dispersion *= inverseCrewImpactFactor; 
        // This includes dispersion.factors like onMove, onHullTurn, onTurretTurn if they exist and are numbers
        if (modifiedStats.dispersion && typeof modifiedStats.dispersion.base === 'number') modifiedStats.dispersion.base *= inverseCrewImpactFactor;
        if (modifiedStats.dispersionFactors) { // Handling new structure like Progetto
            for (const key in modifiedStats.dispersionFactors) {
                if (typeof modifiedStats.dispersionFactors[key] === 'number') {
                    modifiedStats.dispersionFactors[key] *= inverseCrewImpactFactor;
                }
            }
        }

        if (modifiedStats.viewRange) modifiedStats.viewRange *= crewImpactFactor;
        // TODO: Crew bonus also affects driver skills (engine power, traverse) and radio operator (signal range)
    }

    // Recalculate DPM if reloadTime or avgDamage might have changed
    if (modifiedStats.reloadTime && modifiedStats.avgDamage) {
        modifiedStats.dpm = Math.round((modifiedStats.avgDamage / modifiedStats.reloadTime) * 60);
    }

    return modifiedStats;
}

// Экспортируем функции
export {
  loadTanksData,
  analyzeCharacteristicRanges,
  getNormalizedPercentage,
  calculateTankRating,
  compareCharacteristic,
  applyBonusesToTankStats,
  DEFAULT_RANGES
};
