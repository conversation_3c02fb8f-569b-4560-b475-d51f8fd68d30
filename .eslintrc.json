{"env": {"browser": true, "es2021": true, "node": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"no-console": "off", "no-debugger": "warn", "no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "eqeqeq": "warn", "no-eval": "error", "no-implied-eval": "error", "no-new-func": "error", "no-return-assign": "warn", "no-self-compare": "error", "no-throw-literal": "error", "no-unused-expressions": "warn", "no-useless-call": "warn", "no-useless-concat": "warn", "no-useless-return": "warn", "prefer-promise-reject-errors": "warn", "no-var": "warn", "prefer-const": "warn", "prefer-arrow-callback": "warn", "prefer-template": "warn"}, "globals": {"tanksData": "readonly", "TANK_TYPES": "readonly", "TANK_ROLES": "readonly"}}