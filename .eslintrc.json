{"env": {"browser": true, "es2022": true, "node": true}, "extends": ["eslint:recommended", "prettier"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["import"], "rules": {"no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-console": "off", "no-debugger": "warn", "import/no-unresolved": "off", "import/extensions": "off", "prefer-const": "warn", "no-var": "error", "prefer-arrow-callback": "warn", "indent": "off", "quotes": "off", "semi": "off"}, "globals": {"window": "readonly", "document": "readonly", "console": "readonly"}, "ignorePatterns": ["node_modules/", "dist/", "*.min.js", "coverage/"]}