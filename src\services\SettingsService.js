// Сервис для управления настройками приложения
import logger from '../utils/logger.js';

export class SettingsService {
  constructor() {
    this.settings = {
      theme: 'dark',
      language: 'ru',
      fontSize: 'medium',
      cacheEnabled: true
    };
    
    this.loadSettings();
  }

  loadSettings() {
    this.settings.theme = localStorage.getItem('theme') || 'dark';
    this.settings.language = localStorage.getItem('language') || 'ru';
    this.settings.fontSize = localStorage.getItem('fontSize') || 'medium';
    this.settings.cacheEnabled = localStorage.getItem('cacheEnabled') !== 'false';
  }

  saveSettings() {
    Object.entries(this.settings).forEach(([key, value]) => {
      localStorage.setItem(key, value.toString());
    });
  }

  setTheme(theme) {
    logger.log('Theme changed to:', theme);
    this.settings.theme = theme;
    localStorage.setItem('theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
    
    if (theme === 'dark') {
      document.body.classList.add('gray-bg');
    } else {
      document.body.classList.remove('gray-bg');
    }
  }

  setLanguage(language) {
    logger.log('Language changed to:', language);
    this.settings.language = language;
    localStorage.setItem('language', language);
    // TODO: Implement language change logic
  }

  setFontSize(fontSize) {
    logger.log('Font size changed to:', fontSize);
    this.settings.fontSize = fontSize;
    localStorage.setItem('fontSize', fontSize);
    
    const fontSizeMap = {
      'small': '14px',
      'medium': '16px',
      'large': '18px'
    };
    
    document.documentElement.style.setProperty('--base-font-size', fontSizeMap[fontSize] || '16px');
  }

  setCacheEnabled(enabled) {
    logger.log('Cache enabled:', enabled);
    this.settings.cacheEnabled = enabled;
    localStorage.setItem('cacheEnabled', enabled.toString());
  }

  clearCache() {
    localStorage.clear();
    sessionStorage.clear();
    
    // Clear image caches
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          caches.delete(name);
        });
      });
    }
  }

  applySettings() {
    this.setTheme(this.settings.theme);
    this.setFontSize(this.settings.fontSize);
    // Language is applied automatically via UI
  }
}

// Create singleton instance
export const settingsService = new SettingsService();