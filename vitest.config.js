import { defineConfig } from 'vitest/config';
import { resolve } from 'path';

export default defineConfig({
  test: {
    // Тестовая среда
    environment: 'jsdom',
    
    // Файлы настройки
    setupFiles: ['./src/tests/setup.js'],
    
    // Глобальные переменные
    globals: true,
    
    // Покрытие кода
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.test.js',
        '**/*.spec.js',
        'dist/',
        'coverage/',
        '*.config.js'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Таймауты
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // Параллельное выполнение
    threads: true,
    maxThreads: 4,
    
    // Отчеты
    reporter: ['verbose', 'json', 'html'],
    
    // Файлы для включения/исключения
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache'],
    
    // Моки
    mockReset: true,
    clearMocks: true,
    restoreMocks: true
  },
  
  // Алиасы для тестов
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@data': resolve(__dirname, 'src/data'),
      '@services': resolve(__dirname, 'src/services'),
      '@tests': resolve(__dirname, 'src/tests')
    }
  }
});
