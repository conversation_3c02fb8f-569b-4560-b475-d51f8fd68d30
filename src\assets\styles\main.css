/* Main CSS file - ULTRA-OPTIMIZED STRUCTURE */

/* Critical CSS - Load first for performance */
@import './optimized.css';

/* Base styles - Load after critical */
@import './base/variables.css';
@import './base/reset.css';

/* Effects - Load early for better performance */
@import './effects/glassmorphism.css';

/* Component styles - Optimized order */
@import './components/sidebar.css';
@import './components/tank-list.css';
@import './components/equipment.css';
@import './components/loading.css';

/* Page-specific styles - Load last for proper cascade */
@import '../css/tank-characteristics.css';
@import './components/tank-details.css';

/* ========================================
   ДОПОЛНИТЕЛЬНЫЕ ПЕРЕМЕННЫЕ (основные в base/variables.css)
   ======================================== */

:root {
    /* Переопределяем базовые цвета для темной темы */
    --bg-primary: #161b2b;
    --bg-secondary: #1f2942;
    --text-primary: #e2e8f0;
    --text-secondary: #a0aec0;
    --border-color: rgba(139, 92, 246, 0.3);
    --accent-color: #8b5cf6;

    /* Glassmorphism effects - специфичные для проекта */
    --glass-bg: rgba(30, 39, 59, 0.6);
    --glass-border: rgba(139, 92, 246, 0.2);
    --glass-shadow: 0 8px 32px rgba(13, 10, 32, 0.2);
    --glass-blur: 10px;

    /* Unity-inspired Dark Theme - уникальные для проекта */
    --gm-dark-bg: radial-gradient(circle at top right, #1f1543 0%, #150f2d 30%, #0c0920 60%, #050413 100%);
    --gm-dark-bg-secondary: rgba(55, 46, 104, 0.25);
    --gm-dark-bg-accent: rgba(120, 110, 215, 0.3);
    --gm-dark-text-primary: #e6edf3;
    --gm-dark-text-secondary: #b0c1d1;
    --gm-dark-accent: #8a70d6;
    --gm-dark-accent-hover: #a085f0;
    --gm-dark-accent-active: #b59af7;
    --gm-dark-modal-bg: rgba(30, 20, 55, 0.9);
    --gm-dark-progress-track: rgba(50, 30, 100, 0.3);

    /* Tank type colors - Унифицированные */
    --color-lt: #8b5cf6;
    --color-st: #ec4899;
    --color-tt: #3b82f6;
    --color-td: #10b981;
    --color-spg: #ef4444;

    /* Progress bar gradients - специфичные для танков */
    --progress-red: linear-gradient(90deg, #c44569 0%, #e15f41 100%);
    --progress-orange: linear-gradient(90deg, #e15f41 0%, #f5cd79 100%);
    --progress-yellow: linear-gradient(90deg, #f5cd79 0%, #fdcb6e 100%);
    --progress-green: linear-gradient(90deg, #26de81 0%, #2bcbba 100%);
    --progress-blue: linear-gradient(90deg, #45aaf2 0%, #2d98da 100%);
    --progress-purple: linear-gradient(90deg, #8854d0 0%, #a55eea 100%);
}

/* ======================================== 
   BASE STYLES
   ======================================== */

html,
body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    box-sizing: border-box;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    letter-spacing: 0.015em;
    font-weight: 400;
}

*,
*::before,
*::after {
    box-sizing: inherit;
}

body {
    margin: 0;
    padding: 0;
    overflow: hidden; /* Убираем скроллбары с body - пусть main-content-container управляет скроллом */
    background-color: #2a2155;
    background-image:
        radial-gradient(circle at 30% 20%, rgba(85, 64, 174, 0.5) 0%, transparent 45%),
        radial-gradient(circle at 80% 80%, rgba(123, 31, 162, 0.4) 0%, transparent 50%),
        radial-gradient(circle at 10% 60%, rgba(59, 28, 218, 0.3) 0%, transparent 40%),
        radial-gradient(circle at 70% 30%, rgba(172, 69, 230, 0.2) 0%, transparent 35%);
    background-attachment: fixed;
    position: relative;
    min-height: 100vh;
}

/* ======================================== 
   APP CONTAINER
   ======================================== */

.app-container {
    display: flex;
    flex-direction: row;
    width: 100%;
    min-height: 100vh;
    position: relative;
    background-color: inherit;
    box-sizing: border-box;
    overflow-x: hidden;
}

/* ======================================== 
   MAIN CONTENT
   ======================================== */

.main-content-container {
    position: fixed;
    top: 0;
    left: 250px;
    right: 0;
    bottom: 0;
    z-index: 5;
    overflow-y: auto; /* Обычный системный скроллбар */
    overflow-x: hidden;
    padding: 1rem; /* Возвращаем отступы для списка танков */
    display: block;
}

/* Убираем кастомный скроллбар - используем системный */

@media (min-width: 768px) {
    .main-content-container {
        padding: 1.5rem; /* Возвращаем отступы и здесь */
    }
}

/* ======================================== 
   CONTENT SECTIONS
   ======================================== */

.content-section {
    width: 100%;
    height: auto;
    overflow-y: visible;
    min-height: 0;
    padding: 1rem;
    animation: fadeIn 0.3s ease-in-out;
}

.content-section.hidden {
    display: none;
}

.content-section.tank-characteristics {
    min-height: calc(100vh + 500px);
}

.section-container {
    background: var(--glass-bg);
    border-radius: 1rem;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    /* max-width: 1200px; - УБИРАЕМ ОГРАНИЧЕНИЕ ШИРИНЫ */
    margin: 0; /* Убираем auto центрирование */
}

/* ======================================== 
   BUTTONS
   ======================================== */

.btn {
    display: inline-block;
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    text-align: center;
}

.btn:hover {
    background-color: #7c4bd6;
    transform: translateY(-1px);
}

.btn:active {
    transform: translateY(0);
}

/* ========================================
   ANIMATIONS
   ======================================== */

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes expandIn {
    from {
        opacity: 0;
        transform: scale(0.9);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: scale(1);
        max-height: 100vh;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

.hidden {
    display: none;
}

/* Предотвращение моргания при загрузке */
#tank-list {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

#tank-list.loaded {
    opacity: 1;
}

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.text-center {
    text-align: center;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.text-white {
    color: #ffffff;
}

.overflow-auto {
    overflow-y: auto;
    overflow-x: hidden;
}

.min-h-screen {
    min-height: 100vh;
}

.w-full {
    width: 100%;
}

.relative {
    position: relative;
}

.z-10 {
    z-index: 10;
}

.transition-all {
    transition: all 0.3s ease-linear;
}

.duration-300 {
    transition-duration: 300ms;
}

.ease-linear {
    transition-timing-function: linear;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 768px) {
    .main-content-container {
        left: 0;
        padding: 0.5rem;
    }

    .section-container {
        padding: 1rem;
        border-radius: 0.75rem;
    }
}

@media (max-width: 480px) {
    .content-section {
        padding: 0.5rem;
    }

    .section-container {
        padding: 0.75rem;
    }
}
