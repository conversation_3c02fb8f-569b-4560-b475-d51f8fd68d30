/* 
 * ОПТИМИЗИРОВАННЫЕ СТИЛИ - КРИТИЧЕСКИЙ CSS
 * Загружается в первую очередь для улучшения производительности
 */

/* ======================================== 
   КРИТИЧЕСКИЕ СТИЛИ ДЛЯ ПЕРВОЙ ОТРИСОВКИ
   ======================================== */

/* Базовые стили для предотвращения FOUC */
html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', system-ui, sans-serif;
  background-color: #2a2155;
  color: #ffffff;
  overflow: hidden;
}

/* Критические контейнеры */
.app-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
}

.sidebar {
  width: 250px;
  background: linear-gradient(135deg, rgba(32, 25, 55, 0.8), rgba(26, 20, 45, 0.9));
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.main-content-container {
  position: fixed;
  top: 0;
  left: 250px;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem;
}

/* ======================================== 
   ОПТИМИЗИРОВАННЫЕ УТИЛИТЫ
   ======================================== */

/* Быстрые утилиты для скрытия/показа - БЕЗ !important */
.hidden {
  display: none;
}

.invisible {
  visibility: hidden;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

/* Оптимизированные переходы - БЕЗ !important для избежания конфликтов */
.transition-fast {
  transition: all 0.15s ease;
}

.transition-normal {
  transition: all 0.25s ease;
}

.transition-slow {
  transition: all 0.35s ease;
}

/* Специальные переходы - убираем !important */
.force-transition-fast {
  transition: all 0.15s ease;
}

.force-transition-normal {
  transition: all 0.25s ease;
}

.force-transition-slow {
  transition: all 0.35s ease;
}

/* ======================================== 
   ПРОИЗВОДИТЕЛЬНОСТЬ АНИМАЦИЙ
   ======================================== */

/* Оптимизация для GPU ускорения */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* Оптимизированные анимации */
@keyframes fadeInFast {
  from {
    opacity: 0;
    transform: translateY(10px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateX(0) translateZ(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateZ(0);
  }
  to {
    opacity: 1;
    transform: scale(1) translateZ(0);
  }
}

/* Классы для анимаций */
.animate-fade-in {
  animation: fadeInFast 0.3s ease-out both;
}

.animate-slide-in {
  animation: slideInLeft 0.3s ease-out both;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out both;
}

/* ======================================== 
   ОПТИМИЗАЦИЯ ЗАГРУЗКИ
   ======================================== */

/* Скелетон для загрузки */
.skeleton {
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.1) 25%, 
    rgba(255, 255, 255, 0.2) 50%, 
    rgba(255, 255, 255, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Индикатор загрузки */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ======================================== 
   КРИТИЧЕСКИЕ СТИЛИ ТАНКОВ
   ======================================== */

/* Базовая сетка танков */
#tank-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(208px, 1fr));
  gap: 0.75rem;
  padding: 0;
  margin: 4rem 1.5rem 0 0;
}

/* Базовая карточка танка */
.tank-item {
  background: transparent;
  border: 1px solid transparent;
  border-radius: 12px;
  padding: 0.65rem;
  display: flex;
  align-items: stretch;
  gap: 0.65rem;
  cursor: pointer;
  transition: transform 0.25s ease, box-shadow 0.25s ease;
  min-height: 90px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.tank-item:hover {
  transform: translateY(-2px) translateZ(0);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ======================================== 
   МЕДИА-ЗАПРОСЫ ДЛЯ ПРОИЗВОДИТЕЛЬНОСТИ
   ======================================== */

/* Мобильные устройства */
@media (max-width: 768px) {
  .main-content-container {
    left: 0;
    padding: 0.5rem;
  }
  
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
}

/* Уменьшенная анимация для слабых устройств - БЕЗ !important */
@media (prefers-reduced-motion: reduce) {
  *:not(.notes-section):not(.notes-section *),
  *:not(.notes-section):not(.notes-section *)::before,
  *:not(.notes-section):not(.notes-section *)::after {
    animation-duration: 0.01ms;
    animation-iteration-count: 1;
    transition-duration: 0.01ms;
  }

  .skeleton {
    animation: none;
    background: rgba(255, 255, 255, 0.1);
  }

  /* Для примечаний оставляем быструю, но видимую анимацию */
  .notes-section,
  .notes-section * {
    transition-duration: 0.2s;
  }
}

/* Высокая плотность пикселей */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .tank-item {
    border-width: 0.5px;
  }
}

/* ======================================== 
   ОПТИМИЗАЦИЯ ШРИФТОВ
   ======================================== */

/* Предзагрузка критических шрифтов */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuLyfAZ9hiA.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url('https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ======================================== 
   СОДЕРЖИМОЕ ВЫШЕ СГИБА (ABOVE THE FOLD)
   ======================================== */

/* Критические стили для первого экрана */
.content-section {
  width: 100%;
  height: auto;
  padding: 1rem;
  animation: fadeInFast 0.3s ease-in-out;
}

/* Оптимизированные фильтры */
.filter-item {
  transition: background-color 0.15s ease;
  cursor: pointer;
}

.filter-item:hover {
  background-color: rgba(139, 92, 246, 0.1);
}

.filter-item.active {
  background-color: rgba(139, 92, 246, 0.2);
}
