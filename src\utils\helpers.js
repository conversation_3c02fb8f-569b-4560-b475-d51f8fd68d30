/**
 * Helper utilities - only used functions
 * Вспомогательные утилиты - только используемые функции
 */

import { getTankIconPath } from './constants.js';

/**
 * Creates a tank ID from tank name using the constants module
 */
export function createTankId(tankName) {
  // Используем функцию из constants.js для получения имени файла иконки
  const iconPath = getTankIconPath(tankName);
  // Извлекаем имя файла без расширения и пути
  return iconPath.split('/').pop().replace('.webp', '');
}
