// equipment.js
export const STANDARD_EQUIPMENT = {
  'Improved Ventilation': {
    icon: 'src/assets/images/equipment/ventilation_standard.png',
    type: 'standard',
    category: ['firepower','survivability','mobility','reconnaissance'],
    bonuses: {
      base: { crewBonus: 0.05 },
      category: { crewBonus: 0.06 }
    }
  },
  'Gun Rammer': {
    icon: 'src/assets/images/equipment/gun_rammer_standard.png',
    type: 'standard',
    category: 'firepower',
    bonuses: {
      base: { reloadTime: -0.10 },
      category: { reloadTime: -0.115 }
    }
  },
  'Gun Laying Drive': {
    icon: 'src/assets/images/equipment/aiming_drives_standard.png',
    type: 'standard',
    category: 'firepower',
    bonuses: {
      base: { aimTime: -0.10 },
      category: { aimTime: -0.115 }
    }
  },
  'Vertical Stabilizer': {
    icon: 'src/assets/images/equipment/vertical_stabilizer_standard.png',
    type: 'standard',
    category: 'firepower',
    bonuses: {
      base: { dispersion: -0.20 },
      category: { dispersion: -0.23 }
    }
  },
  'Improved Aiming Scope': {
    icon: 'src/assets/images/equipment/scope_standard.png',
    type: 'standard',
    category: 'firepower',
    bonuses: {
      base: { aimTime: -0.05 },
      category: { aimTime: -0.07 }
    }
  },
  'Inertia Mechanism': {
    icon: 'src/assets/images/equipment/traverse_standard.png',
    type: 'standard',
    category: ['firepower','mobility'],
    bonuses: {
      base: { dispersion: -0.10, speed: 0.10 },
      category: { dispersion: -0.125, speed: 0.125 }
    }
  },
  'Additional Track Links': {
    icon: 'src/assets/images/equipment/extra_grousers_standard.png',
    type: 'standard',
    category: 'mobility',
    bonuses: {
      base: { speed: 0.15, acceleration: 0.10 },
      category: { speed: 0.20, acceleration: 0.15 }
    }
  },
  'Turbocharger': {
    icon: 'src/assets/images/equipment/turbocharger_standard.png',
    type: 'standard',
    category: 'mobility',
    bonuses: {
      base: { enginePower: 0.075, speedForward: 4, speedReverse: 2 },
      category: { enginePower: 0.10, speedForward: 5, speedReverse: 3 }
    }
  },
  'Binocular Telescope': {
    icon: 'src/assets/images/equipment/binocular_telescope_standard.png',
    type: 'standard',
    category: 'reconnaissance',
    mode: 'static',
    bonuses: {
      base: { viewRange: 0.25 },
      category: { viewRange: 0.275 }
    }
  },
  'Camouflage Net': {
    icon: 'src/assets/images/equipment/camouflage_net_standard.png',
    type: 'standard',
    category: 'reconnaissance',
    mode: 'static',
    bonuses: {
      base: {
        camouflage: {
          heavy_artillery: 0.05,
          light_medium: 0.10,
          td: 0.15
        }
      },
      category: {
        camouflage: {
          heavy_artillery: 0.075,
          light_medium: 0.125,
          td: 0.175
        }
      }
    }
  },
  'Low-Noise Exhaust System': {
    icon: 'src/assets/images/equipment/exhaust_standard.png',
    type: 'standard',
    category: 'reconnaissance',
    bonuses: {
      base: {
        camouflage: {
          heavy_artillery: 0.03,
          light_medium: 0.06,
          td: 0.05
        }
      },
      category: {
        camouflage: {
          heavy_artillery: 0.04,
          light_medium: 0.08,
          td: 0.06
        }
      }
    }
  },
  'Clear Optics': {
    icon: 'src/assets/images/equipment/optics_standard.png',
    type: 'standard',
    category: 'reconnaissance',
    mode: 'always',
    bonuses: {
      base: { viewRange: 0.10 },
      category: { viewRange: 0.115 }
    }
  },
  'Improved Radio Equipment': {
    icon: 'src/assets/images/equipment/radio_standard.png',
    type: 'standard',
    category: 'reconnaissance',
    bonuses: {
      base: { enemyDetectTime: 1.5, ownDetectTime: -1.5 },
      category: { enemyDetectTime: 2.0, ownDetectTime: -2.0 }
    }
  },
  "Commander's Observation Device": {
    icon: 'src/assets/images/equipment/commanders_periscope_standard.png',
    type: 'standard',
    category: 'reconnaissance',
    bonuses: {
      base: { camouflageMoving: -0.10, camouflageVegetation: -0.15 },
      category: { camouflageMoving: -0.125, camouflageVegetation: -0.20 }
    }
  },
  'Spall Liner': {
    icon: 'src/assets/images/equipment/spall_liner_standard.png',
    type: 'standard',
    category: 'survivability',
    bonuses: {
      base: { moduleDamageReduction: 0.50, crewProtection: 0.50, stunDuration: -0.10 },
      category: { moduleDamageReduction: 0.60, crewProtection: 0.60, stunDuration: -0.15 }
    }
  },
  'Enhanced Hardening': {
    icon: 'src/assets/images/equipment/hardening_standard.png',
    type: 'standard',
    category: 'survivability',
    bonuses: {
      base: { hullDurability: 0.08, trackDurability: 0.50, fallDamageReduction: -0.50, repairSpeed: 0.15 },
      category: { hullDurability: 0.10, trackDurability: 0.65, fallDamageReduction: -0.65, repairSpeed: 0.20 }
    }
  },
  'Modified Layout': {
    icon: 'src/assets/images/equipment/modified_layout_standard.png',
    type: 'standard',
    category: 'survivability',
    bonuses: {
      base: { moduleProtection: 1.00, damageReduction: -0.50, fireProtection: -0.50, repairSpeed: 0.25 },
      category: { moduleProtection: 1.50, damageReduction: -0.65, fireProtection: -0.65, repairSpeed: 0.35 }
    }
  }
};

export const SPECIAL_EQUIPMENT = {
  'Trophy Ventilation': {
    icon: 'src/assets/images/equipment/ventilation_trophy.png',
    type: 'special',
    category: ['firepower','survivability','mobility','reconnaissance'],
    bonuses: { crewBonus: 0.05 },
    upgrade: { cost: 3000000, bonuses: { crewBonus: 0.075 } },
    restrictions: { excludeVehicles: ['open-top'], excludeEquipment: ['Improved Ventilation'] }
  },
  'Trophy Gun Rammer': {
    icon: 'src/assets/images/equipment/gun_rammer_trophy.png',
    type: 'special',
    category: 'firepower',
    bonuses: { reloadTime: -0.10 },
    upgrade: { cost: 3000000, bonuses: { reloadTime: -0.125 } },
    restrictions: { excludeEquipment: ['Gun Laying Drive'] }
  },
  'Trophy Stabilizer': {
    icon: 'src/assets/images/equipment/vertical_stabilizer_trophy.png',
    type: 'special',
    category: 'firepower',
    bonuses: { dispersion: -0.20 },
    upgrade: { cost: 3000000, bonuses: { dispersion: -0.25 } },
    restrictions: { excludeEquipment: ['Vertical Stabilizer'] }
  },
  'Trophy Turbocharger': {
    icon: 'src/assets/images/equipment/turbocharger_trophy.png',
    type: 'special',
    category: 'mobility',
    bonuses: { enginePower: 0.075, speedForward: 4, speedReverse: 2 },
    upgrade: { cost: 3000000, bonuses: { enginePower: 0.12, speedForward: 5, speedReverse: 3 } },
    restrictions: { excludeVehicles: ['wheeled'] }
  },
  'Trophy Optics': {
    icon: 'src/assets/images/equipment/optics_trophy.png',
    type: 'special',
    category: 'reconnaissance',
    bonuses: { viewRange: 0.10 },
    upgrade: { cost: 3000000, bonuses: { viewRange: 0.12 } },
    restrictions: { excludeEquipment: ['Clear Optics'] }
  },
  'Trophy Aiming Drives': {
    icon: 'src/assets/images/equipment/aiming_drives_trophy.png',
    type: 'special',
    category: 'firepower',
    bonuses: { aimTime: -0.10 },
    upgrade: { cost: 3000000, bonuses: { aimTime: -0.125 } },
    restrictions: { excludeEquipment: ['Gun Laying Drive'] }
  },
  'Trophy Scope': {
    icon: 'src/assets/images/equipment/scope_trophy.png',
    type: 'special',
    category: 'firepower',
    bonuses: { dispersion: -0.05 },
    upgrade: { cost: 3000000, bonuses: { dispersion: -0.08 } },
    restrictions: { excludeEquipment: ['Improved Aiming Scope'] }
  },
  'Trophy Traverse Mechanism': {
    icon: 'src/assets/images/equipment/traverse_trophy.png',
    type: 'special',
    category: ['firepower','mobility'],
    bonuses: { dispersion: -0.10, speed: 0.10 },
    upgrade: { cost: 3000000, bonuses: { dispersion: -0.15, speed: 0.15 } }
  },
  'Trophy Exhaust System': {
    icon: 'src/assets/images/equipment/exhaust_trophy.png',
    type: 'special',
    category: 'reconnaissance',
    bonuses: { camouflage: 0.06 },
    upgrade: { cost: 3000000, bonuses: { camouflage: 0.09 } }
  },
  'Trophy Hardening': {
    icon: 'src/assets/images/equipment/hardening_trophy.png',
    type: 'special',
    category: 'survivability',
    bonuses: { hullDurability: 0.08, trackDurability: 0.50, repairSpeed: 0.15, fallDamageReduction: -0.50 },
    upgrade: { cost: 3000000, bonuses: { hullDurability: 0.11, trackDurability: 0.70, repairSpeed: 0.25, fallDamageReduction: -0.70 } }
  },
  'Trophy Modified Layout': {
    icon: 'src/assets/images/equipment/modified_layout_trophy.png',
    type: 'special',
    category: 'survivability',
    bonuses: { repairSpeed: 0.25, moduleProtection: 1.00, damageReduction: -0.50, fireProtection: -0.50 },
    upgrade: { cost: 3000000, bonuses: { repairSpeed: 0.40, moduleProtection: 1.50, damageReduction: -0.70, fireProtection: -0.70 } },
    restrictions: { excludeEquipment: ['Modified Layout'] }
  }
};

export const IMPROVED_EQUIPMENT = {
  'Improved Ventilation': {
    icon: 'src/assets/images/equipment/ventilation_improved.png',
    type: 'improved',
    category: ['firepower','survivability','mobility','reconnaissance'],
    bonuses: {
      base: { crewBonus: 0.085 },
      category: { crewBonus: 0.085 }
    },
    price: { bon: 5000 }
  },
  'Gun Laying Drive': {
    icon: 'src/assets/images/equipment/aiming_drives_improved.png',
    type: 'improved',
    category: 'firepower',
    bonuses: {
      base: { aimTime: -0.135 },
      category: { aimTime: -0.135 }
    },
    price: { bon: 5000 }
  },
  'Gun Rammer': {
    icon: 'src/assets/images/equipment/gun_rammer_improved.png',
    type: 'improved',
    category: 'firepower',
    bonuses: {
      base: { reloadTime: -0.135 },
      category: { reloadTime: -0.135 }
    },
    price: { bon: 5000 }
  },
  'Vertical Stabilizer': {
    icon: 'src/assets/images/equipment/vertical_stabilizer_improved.png',
    type: 'improved',
    category: 'firepower',
    bonuses: {
      base: { dispersion: -0.275 },
      category: { dispersion: -0.275 }
    },
    price: { bon: 5000 }
  },
  'Improved Aiming Scope': {
    icon: 'src/assets/images/equipment/scope_improved.png',
    type: 'improved',
    category: 'firepower',
    bonuses: {
      base: { aimTime: -0.135 },
      category: { aimTime: -0.135 }
    },
    price: { bon: 5000 }
  },
  'Turbocharger': {
    icon: 'src/assets/images/equipment/turbocharger_improved.png',
    type: 'improved',
    category: 'mobility',
    bonuses: {
      base: { enginePower: 0.125, speedForward: 6, speedReverse: 4 },
      category: { enginePower: 0.125, speedForward: 6, speedReverse: 4 }
    },
    price: { bon: 5000 }
  },
  'Clear Optics': {
    icon: 'src/assets/images/equipment/optics_improved.png',
    type: 'improved',
    category: 'reconnaissance',
    bonuses: {
      base: { viewRange: 0.135 },
      category: { viewRange: 0.135 }
    },
    price: { bon: 5000 }
  },
  'Enhanced Hardening': {
    icon: 'src/assets/images/equipment/hardening_improved.png',
    type: 'improved',
    category: 'survivability',
    bonuses: {
      base: { hullDurability: 0.125, trackDurability: 0.75, fallDamageReduction: -0.75, repairSpeed: 0.30 },
      category: { hullDurability: 0.125, trackDurability: 0.75, fallDamageReduction: -0.75, repairSpeed: 0.30 }
    },
    price: { bon: 5000 }
  },
  'Modified Layout': {
    icon: 'src/assets/images/equipment/modified_layout_improved.png',
    type: 'improved',
    category: 'survivability',
    bonuses: {
      base: { moduleProtection: 1.50, damageReduction: -0.70, fireProtection: -0.70, repairSpeed: 0.45 },
      category: { moduleProtection: 1.50, damageReduction: -0.70, fireProtection: -0.70, repairSpeed: 0.45 }
    },
    price: { bon: 5000 }
  },
  'Inertia Mechanism': {
    icon: 'src/assets/images/equipment/traverse_improved.png',
    type: 'improved',
    category: ['firepower','mobility'],
    bonuses: {
      base: { dispersion: -0.175, speed: 0.175 },
      category: { dispersion: -0.175, speed: 0.175 }
    },
    price: { bon: 5000 }
  }
};

export const EXPERIMENTAL_EQUIPMENT = {
  'Система повышения мобильности T1': {
    icon: 'src/assets/images/equipment/Mobility_Enhancement_System_T1.png',
    type: 'experimental',
    category: 'mobility',
    bonuses: {
      base: { enginePower: 0.06, speed: 0.05, speedForward: 4, speedReverse: 2, dispersion: -0.05 },
      category: { enginePower: 0.06, speed: 0.05, speedForward: 4, speedReverse: 2, dispersion: -0.05 }
    },
    price: { components: 100 },
    upgrade: {
      to: 'Система повышения мобильности T2',
      cost: 400,
      bonuses: {
        base: { enginePower: 0.075, speed: 0.06, speedForward: 4, speedReverse: 3, dispersion: -0.06 },
        category: { enginePower: 0.075, speed: 0.06, speedForward: 4, speedReverse: 3, dispersion: -0.06 }
      }
    }
  },
  'Система повышения мобильности T2': {
    icon: 'src/assets/images/equipment/Mobility_Enhancement_System_T2.png',
    type: 'experimental',
    category: 'mobility',
    bonuses: {
      base: { enginePower: 0.075, speed: 0.06, speedForward: 4, speedReverse: 3, dispersion: -0.06 },
      category: { enginePower: 0.075, speed: 0.06, speedForward: 4, speedReverse: 3, dispersion: -0.06 }
    },
    price: { components: 400 },
    upgrade: {
      to: 'Система повышения мобильности T3',
      cost: 2000,
      bonuses: {
        base: { enginePower: 0.09, speed: 0.07, speedForward: 5, speedReverse: 3, dispersion: -0.07 },
        category: { enginePower: 0.09, speed: 0.07, speedForward: 5, speedReverse: 3, dispersion: -0.07 }
      }
    }
  },
  'Система повышения мобильности T3': {
    icon: 'src/assets/images/equipment/Mobility_Enhancement_System_T3.png',
    type: 'experimental',
    category: 'mobility',
    bonuses: {
      base: { enginePower: 0.09, speed: 0.07, speedForward: 5, speedReverse: 3, dispersion: -0.07 },
      category: { enginePower: 0.09, speed: 0.07, speedForward: 5, speedReverse: 3, dispersion: -0.07 }
    },
    price: { components: 2000 }
  },
  'Комплекс улучшения выживаемости T1': {
    icon: 'src/assets/images/equipment/Survival_Enhancement_Complex_T1.png',
    type: 'experimental',
    category: 'survivability',
    bonuses: {
      base: { hullDurability: 0.08, moduleProtection: 0.50, trackDurability: 0.30, repairSpeed: 0.10, damagedPowerPenalty: -0.30 },
      category: { hullDurability: 0.08, moduleProtection: 0.50, trackDurability: 0.30, repairSpeed: 0.10, damagedPowerPenalty: -0.30 }
    },
    price: { components: 100 },
    upgrade: {
      to: 'Комплекс улучшения выживаемости T2',
      cost: 400,
      bonuses: {
        base: { hullDurability: 0.09, moduleProtection: 0.70, trackDurability: 0.40, repairSpeed: 0.15, damagedPowerPenalty: -0.40 },
        category: { hullDurability: 0.09, moduleProtection: 0.70, trackDurability: 0.40, repairSpeed: 0.15, damagedPowerPenalty: -0.40 }
      }
    }
  },
  'Комплекс улучшения выживаемости T2': {
    icon: 'src/assets/images/equipment/Survival_Enhancement_Complex_T2.png',
    type: 'experimental',
    category: 'survivability',
    bonuses: {
      base: { hullDurability: 0.09, moduleProtection: 0.70, trackDurability: 0.40, repairSpeed: 0.15, damagedPowerPenalty: -0.40 },
      category: { hullDurability: 0.09, moduleProtection: 0.70, trackDurability: 0.40, repairSpeed: 0.15, damagedPowerPenalty: -0.40 }
    },
    price: { components: 400 },
    upgrade: {
      to: 'Комплекс улучшения выживаемости T3',
      cost: 2000,
      bonuses: {
        base: { hullDurability: 0.11, moduleProtection: 1.00, trackDurability: 0.50, repairSpeed: 0.25, damagedPowerPenalty: -0.50 },
        category: { hullDurability: 0.11, moduleProtection: 1.00, trackDurability: 0.50, repairSpeed: 0.25, damagedPowerPenalty: -0.50 }
      }
    }
  },
  'Комплекс улучшения выживаемости T3': {
    icon: 'src/assets/images/equipment/Survival_Enhancement_Complex_T3.png',
    type: 'experimental',
    category: 'survivability',
    bonuses: {
      base: { hullDurability: 0.11, moduleProtection: 1.00, trackDurability: 0.50, repairSpeed: 0.25, damagedPowerPenalty: -0.50 },
      category: { hullDurability: 0.11, moduleProtection: 1.00, trackDurability: 0.50, repairSpeed: 0.25, damagedPowerPenalty: -0.50 }
    },
    price: { components: 2000 }
  },
  'Система управления огнём T1': {
    icon: 'src/assets/images/equipment/Fire_control_system_T1.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: {
      base: { aimTime: -0.06, dispersion: -0.07 },
      category: { aimTime: -0.06, dispersion: -0.07 }
    },
    price: { components: 100 },
    upgrade: {
      to: 'Система управления огнём T2',
      cost: 400,
      bonuses: {
        base: { aimTime: -0.08, dispersion: -0.09 },
        category: { aimTime: -0.08, dispersion: -0.09 }
      }
    }
  },
  'Система управления огнём T2': {
    icon: 'src/assets/images/equipment/Fire_control_system_T2.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { aimTime: -0.08, dispersion: -0.09 }, category: { aimTime: -0.08, dispersion: -0.09 } },
    price: { components: 400 },
    upgrade: {
      to: 'Система управления огнём T3',
      cost: 2000,
      bonuses: { base: { aimTime: -0.09, dispersion: -0.115 }, category: { aimTime: -0.09, dispersion: -0.115 } }
    }
  },
  'Система управления огнём T3': {
    icon: 'src/assets/images/equipment/Fire_control_system_T3.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { aimTime: -0.09, dispersion: -0.115 }, category: { aimTime: -0.09, dispersion: -0.115 } },
    price: { components: 2000 }
  },
  'Расточка каморы T1': {
    icon: 'src/assets/images/equipment/Bore_boring_T1.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { gun: { damage: { avg: 0.04 } }, crewBonus: 0.03 }, category: { gun: { damage: { avg: 0.04 } }, crewBonus: 0.03 } },
    price: { components: 100 },
    upgrade: {
      to: 'Расточка каморы T2',
      cost: 400,
      bonuses: { base: { gun: { damage: { avg: 0.05 } }, crewBonus: 0.035 }, category: { gun: { damage: { avg: 0.05 } }, crewBonus: 0.035 } }
    }
  },
  'Расточка каморы T2': {
    icon: 'src/assets/images/equipment/Bore_boring_T2.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { gun: { damage: { avg: 0.05 } }, crewBonus: 0.035 }, category: { gun: { damage: { avg: 0.05 } }, crewBonus: 0.035 } },
    price: { components: 400 },
    upgrade: {
      to: 'Расточка каморы T3',
      cost: 2000,
      bonuses: { base: { gun: { damage: { avg: 0.06 } }, crewBonus: 0.04 }, category: { gun: { damage: { avg: 0.06 } }, crewBonus: 0.04 } }
    }
  },
  'Расточка каморы T3': {
    icon: 'src/assets/images/equipment/Bore_boring_T3.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { gun: { damage: { avg: 0.06 } }, crewBonus: 0.04 }, category: { gun: { damage: { avg: 0.06 } }, crewBonus: 0.04 } },
    price: { components: 2000 }
  },
  'Многозубая каретка досылателя T1': {
    icon: 'src/assets/images/equipment/Multi-tooth_refill_carriage_T1.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { reloadTime: -0.08, aimCircleSize: -0.03 }, category: { reloadTime: -0.08, aimCircleSize: -0.03 } },
    price: { components: 100 },
    upgrade: {
      to: 'Многозубая каретка досылателя T2',
      cost: 400,
      bonuses: { base: { reloadTime: -0.09, aimCircleSize: -0.04 }, category: { reloadTime: -0.09, aimCircleSize: -0.04 } }
    }
  },
  'Многозубая каретка досылателя T2': {
    icon: 'src/assets/images/equipment/Multi-tooth_refill_carriage_T2.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { reloadTime: -0.09, aimCircleSize: -0.04 }, category: { reloadTime: -0.09, aimCircleSize: -0.04 } },
    price: { components: 400 },
    upgrade: {
      to: 'Многозубая каретка досылателя T3',
      cost: 2000,
      bonuses: { base: { reloadTime: -0.10, aimCircleSize: -0.05 }, category: { reloadTime: -0.10, aimCircleSize: -0.05 } }
    }
  },
  'Многозубая каретка досылателя T3': {
    icon: 'src/assets/images/equipment/Multi-tooth_refill_carriage_T3.png',
    type: 'experimental',
    category: 'firepower',
    bonuses: { base: { reloadTime: -0.10, aimCircleSize: -0.05 }, category: { reloadTime: -0.10, aimCircleSize: -0.05 } },
    price: { components: 2000 }
  }
};
