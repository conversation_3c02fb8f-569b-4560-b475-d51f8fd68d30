// UI utility functions for managing sections and sidebar
import { state } from '../store/state.js';

// Map sidebar menu keys to their corresponding container IDs/classes.
export const SECTION_MAP = {
  overview: 'overview-section',   // General info page
  vehicles: 'tank-list',          // Tank list grid (main content columns)
  compare: 'compare-section',     // Compare tanks UI
  settings: 'settings-section',   // Settings/config page
  'tank-characteristics': 'tank-characteristics-container', // Tank characteristics page
};

/**
 * Hide all main content containers defined in SECTION_MAP by
 * adding the `hidden` class and setting display to none.
 * Also closes any open tank detail windows.
 */
export function hideAllSections() {
  Object.values(SECTION_MAP).forEach((id) => {
    const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
    if (el) {
      el.style.setProperty('display', 'none', 'important');
      el.style.setProperty('opacity', '0', 'important');
      el.style.setProperty('visibility', 'hidden', 'important');
      el.classList.add('hidden');
    }
  });

  // Close tank detail windows when switching tabs
  closeTankDetailWindows();
}

/**
 * Close any open tank detail windows/panels
 */
function closeTankDetailWindows() {
  // Hide tank characteristics container
  const tankCharacteristicsContainer = document.getElementById('tank-characteristics-container');
  if (tankCharacteristicsContainer) {
    tankCharacteristicsContainer.style.setProperty('display', 'none', 'important');
    tankCharacteristicsContainer.style.setProperty('opacity', '0', 'important');
    tankCharacteristicsContainer.style.setProperty('visibility', 'hidden', 'important');
    tankCharacteristicsContainer.classList.add('hidden');
  }

  // Старый контейнер деталей танка удален, больше не нужно его скрывать

  // Hide any modal windows
  const modals = document.querySelectorAll('.modal:not(.hidden)');
  modals.forEach(modal => {
    modal.classList.add('hidden');
    modal.style.display = 'none';
  });

  // Reset tank selection state if available (legacy support)
  if (typeof window !== 'undefined' && window.appState) {
    window.appState.selectedTank = null;
  }

  // Clear localStorage tank selection
  if (typeof localStorage !== 'undefined') {
    localStorage.removeItem('selectedTank');
  }

  // Clear URL hash if it contains tank name
  if (typeof window !== 'undefined' && window.location.hash) {
    window.history.replaceState(null, '', window.location.pathname + window.location.search);
  }
}

/**
 * Show a specific section based on sidebar menu key. Uses sensible default
 * display types: `grid` for the tank list and `block` for everything else.
 * @param {string} menu - One of the keys of SECTION_MAP (overview | vehicles | compare | settings)
 */
export function showSection(menu) {
  const id = SECTION_MAP[menu];
  if (!id) return;
  const el = document.getElementById(id) || document.querySelector(`#${id}, .${id}`);
  if (el) {
    // Используем CSS классы вместо !important
    el.style.display = menu === 'vehicles' ? 'grid' : 'block';
    el.style.opacity = '1';
    el.style.visibility = 'visible';
    el.classList.remove('hidden');
    el.classList.add('section-visible');
  }

  // Для секции vehicles заполняем список танков
  if (menu === 'vehicles' && typeof window.applyFiltersAndRenderTankList === 'function') {
    // Используем requestAnimationFrame для плавного отображения
    requestAnimationFrame(() => {
      window.applyFiltersAndRenderTankList();
    });
  }
}

/**
 * Setup sidebar menu items with click handlers
 */
export function setupSidebarMenuItems() {
  const sidebar = document.querySelector('.sidebar');
  if (!sidebar) {
    console.warn('[sidebar] .sidebar element not found.');
    return;
  }

  // Setup logo spin with random direction and speed
  const logo = sidebar.querySelector('.sidebar-logo-icon');
  if (logo) {
    let spinOutTimeout;
    // Removed unused variable: currentRotation

    // Функция для получения случайной анимации вращения
    const getRandomSpinAnimation = () => {
      const directions = ['spinClockwise', 'spinCounterClockwise'];

      // 30% шанс для очень быстрого вращения
      const isSuperFast = Math.random() < 0.3;

      let speeds;
      if (isSuperFast) {
        // Очень быстрые скорости (30% шанс)
        speeds = [
          { name: 'spinFast', duration: '0.4s' },
          { name: 'spinFast', duration: '0.5s' },
          { name: 'spinFast', duration: '0.6s' }
        ];
      } else {
        // Обычные скорости (70% шанс)
        speeds = [
          { name: 'spinFast', duration: '0.8s' },
          { name: 'spin', duration: '1.2s' },
          { name: 'spinSlow', duration: '1.8s' }
        ];
      }

      const direction = directions[Math.floor(Math.random() * directions.length)];
      const speed = speeds[Math.floor(Math.random() * speeds.length)];

      return {
        animation: direction === 'spinClockwise' ? speed.name : 'spinCounterClockwise',
        duration: speed.duration,
        isSuperFast: isSuperFast
      };
    };

    const smoothStop = () => {
      // Останавливаем текущую анимацию и получаем текущий угол поворота
      const computedStyle = window.getComputedStyle(logo);
      const transform = computedStyle.transform;

      // Плавная остановка с возвратом в исходное положение
      logo.style.animation = 'none';
      logo.style.transform = transform; // Фиксируем текущее положение

      // Запускаем плавную анимацию возврата к 0 градусов
      requestAnimationFrame(() => {
        logo.style.transition = 'transform 1.2s cubic-bezier(0.4, 0, 0.2, 1)';
        logo.style.transform = 'rotate(0deg)';
        logo.style.animation = 'glowPulse 2.6s ease-in-out infinite';

        // Убираем transition после завершения анимации
        setTimeout(() => {
          logo.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
          logo.classList.remove('super-fast-spin'); // Убираем класс супер-быстрого вращения
          // currentRotation = 0; // Removed unused variable
        }, 1200);
      });
    };

    logo.addEventListener('mouseenter', () => {
      clearTimeout(spinOutTimeout);
      // Сбрасываем transition для плавного перехода к анимации
      logo.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
      logo.style.transform = '';
      // Получаем случайную анимацию
      const randomSpin = getRandomSpinAnimation();

      // Если супер-быстрое вращение, добавляем более яркое свечение
      if (randomSpin.isSuperFast) {
        console.log('🚀 Супер-быстрое вращение активировано! (30% шанс)', randomSpin.duration);
        // Более яркое свечение для супер-быстрого вращения
        logo.style.animation = `${randomSpin.animation} ${randomSpin.duration} linear infinite, glowPulse 1.8s ease-in-out infinite`;
        // Добавляем временный класс для дополнительных эффектов
        logo.classList.add('super-fast-spin');
      } else {
        console.log('⚡ Обычное вращение', randomSpin.duration);
        // Обычное свечение
        logo.style.animation = `${randomSpin.animation} ${randomSpin.duration} linear infinite, glowPulse 2.6s ease-in-out infinite`;
        logo.classList.remove('super-fast-spin');
      }
    });

    logo.addEventListener('mouseleave', () => {
      spinOutTimeout = setTimeout(smoothStop, 2000);
    });
  }

  // Attach delegated click handler
  sidebar.addEventListener('click', (e) => {
    const item = e.target.closest('.sidebar-menu-item');
    if (!item) return;
    e.preventDefault();
    const section = item.getAttribute('data-section');
    if (!section) return;
    let menu = section;
    // If Vehicles is already active and clicked again, switch to overview (toggle off)
    if (menu === 'vehicles' && state.currentMenuName === 'vehicles') {
      menu = 'overview';
    }
    onMenuSelected(menu);
  });

  // Initial activation based on saved or default menu
  const saved = localStorage.getItem('activeMenuItem') || 'overview';
  onMenuSelected(saved, true);
}

function onMenuSelected(menu, _initial = false) {
  localStorage.setItem('activeMenuItem', menu);
  state.currentMenuName = menu;
  updateActiveClass(menu);
  hideAllSections();
  showSection(menu);

  // Show/hide flag section with animation
  const flagSection = document.getElementById('flag-section');
  if (flagSection) {
    flagSection.style.display = 'block'; // ensure element exists for animation
    if (menu === 'vehicles') {
      flagSection.classList.add('open');
    } else {
      flagSection.classList.remove('open');
    }
  }

  // Убираем дублирующийся вызов - список танков уже показывается в showSection()
}

function updateActiveClass(menu) {
  document.querySelectorAll('.sidebar-menu-item').forEach(item => {
    const section = item.getAttribute('data-section');
    if (!section) return;
    const isActive = section === menu;
    item.classList.toggle('active', isActive);
  });
}
