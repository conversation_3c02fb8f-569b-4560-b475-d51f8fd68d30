/**
 * Tests for performance utilities
 * Тесты для утилит производительности
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  getCachedElement, 
  clearDOMCache, 
  getDebounced,
  performanceMonitor,
  scheduleAnimation
} from '../utils/performance.js';

describe('Performance Utils', () => {
  beforeEach(() => {
    clearDOMCache();
    document.body.innerHTML = '';
  });

  describe('getCachedElement', () => {
    it('should cache DOM elements', () => {
      // Arrange
      const div = document.createElement('div');
      div.id = 'test-element';
      document.body.appendChild(div);

      // Act
      const element1 = getCachedElement('#test-element');
      const element2 = getCachedElement('#test-element');

      // Assert
      expect(element1).toBe(div);
      expect(element2).toBe(div);
      expect(element1).toBe(element2); // Same reference from cache
    });

    it('should return null for non-existent elements', () => {
      // Act
      const element = getCachedElement('#non-existent');

      // Assert
      expect(element).toBeNull();
    });
  });

  describe('getDebounced', () => {
    it('should debounce function calls', async () => {
      // Arrange
      const mockFn = vi.fn();
      const debouncedFn = getDebounced('test', mockFn, 100);

      // Act
      debouncedFn();
      debouncedFn();
      debouncedFn();

      // Assert - function should not be called immediately
      expect(mockFn).not.toHaveBeenCalled();

      // Wait for debounce delay
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Assert - function should be called only once
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('should cache debounced functions', () => {
      // Arrange & Act
      const debouncedFn1 = getDebounced('test-key', () => {}, 100);
      const debouncedFn2 = getDebounced('test-key', () => {}, 100);

      // Assert
      expect(debouncedFn1).toBe(debouncedFn2);
    });
  });

  describe('PerformanceMonitor', () => {
    it('should measure execution time', () => {
      // Arrange
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      performanceMonitor.startMeasure('test-operation');
      performanceMonitor.endMeasure('test-operation');

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('⚡ test-operation:')
      );

      consoleSpy.mockRestore();
    });

    it('should return duration when ending measurement', () => {
      // Act
      performanceMonitor.startMeasure('test-duration');
      const duration = performanceMonitor.endMeasure('test-duration');

      // Assert
      expect(typeof duration).toBe('number');
      expect(duration).toBeGreaterThanOrEqual(0);
    });
  });

  describe('scheduleAnimation', () => {
    it('should execute animation callbacks', async () => {
      // Arrange
      const mockCallback = vi.fn();

      // Act
      scheduleAnimation(mockCallback, 'high');

      // Wait for next frame
      await new Promise(resolve => requestAnimationFrame(resolve));

      // Assert
      expect(mockCallback).toHaveBeenCalled();
    });

    it('should handle callback errors gracefully', async () => {
      // Arrange
      const errorCallback = vi.fn(() => {
        throw new Error('Test error');
      });
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      scheduleAnimation(errorCallback);

      // Wait for next frame
      await new Promise(resolve => requestAnimationFrame(resolve));

      // Assert
      expect(errorCallback).toHaveBeenCalled();
      expect(consoleSpy).toHaveBeenCalledWith(
        'Animation callback error:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });
});
