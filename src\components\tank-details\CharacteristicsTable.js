import { DEFAULT_RANGES } from '../../services/TankAnalyzer.js';

// Обновление характеристик танка
export function updateCharacteristics(characteristics) {
  if (!characteristics) {
    console.warn("No characteristics data provided");
    hideAllProgressBars();
    return;
  }
  
  // Определяем диапазоны для разных характеристик
  const ranges = {
    dpm: DEFAULT_RANGES.dpm || { min: 1800, max: 3800, inverse: false },
    damage: DEFAULT_RANGES.damage || { min: 320, max: 750, inverse: false },
    penetration: DEFAULT_RANGES.penetration || { min: 200, max: 340, inverse: false },
    reloadTime: DEFAULT_RANGES.reloadTime || { min: 20, max: 5, inverse: true },
    aimTime: DEFAULT_RANGES.aimTime || { min: 4.0, max: 1.5, inverse: true },
    dispersion: DEFAULT_RANGES.dispersion || { min: 0.5, max: 0.25, inverse: true },
    viewRange: DEFAULT_RANGES.viewRange || { min: 350, max: 450, inverse: false },
    speed: DEFAULT_RANGES.speed || { min: 30, max: 70, inverse: false },
    reverseSpeed: DEFAULT_RANGES.reverseSpeed || { min: 10, max: 25, inverse: false },
    enginePower: DEFAULT_RANGES.enginePower || { min: 400, max: 1500, inverse: false },
    powerToWeight: DEFAULT_RANGES.powerToWeight || { min: 10, max: 30, inverse: false },
    traverse: DEFAULT_RANGES.traverse || { min: 20, max: 60, inverse: false },
    radioRange: DEFAULT_RANGES.radioRange || { min: 400, max: 800, inverse: false },
    fireChance: DEFAULT_RANGES.fireChance || { min: 30, max: 10, inverse: true },
    hullFront: DEFAULT_RANGES.hullFront || { min: 50, max: 300, inverse: false },
    turretFront: DEFAULT_RANGES.turretFront || { min: 50, max: 400, inverse: false }
  };
  
  // Обновляем основные характеристики
  updateFirepowerCharacteristics(characteristics, ranges);
  updateMobilityCharacteristics(characteristics, ranges);
  updateSurvivabilityCharacteristics(characteristics, ranges);
  updateSpottingCharacteristics(characteristics, ranges);
}

// Обновление характеристик огневой мощи
function updateFirepowerCharacteristics(characteristics, ranges) {
  updateCharValue('dpm', characteristics.dpm, '', ranges.dpm);
  updateCharValue('damage', characteristics.damage, '', ranges.damage);
  updateCharValue('penetration', characteristics.penetration, '', ranges.penetration);
  updateCharValue('reloadTime', characteristics.reloadTime, 'с', ranges.reloadTime);
  updateCharValue('rateOfFire', characteristics.rateOfFire, 'выстр/мин', { min: 4, max: 15, inverse: false });
  updateCharValue('aimTime', characteristics.aimTime, 'с', ranges.aimTime);
  updateCharValue('dispersion', characteristics.dispersion, 'м', ranges.dispersion);
}

// Обновление характеристик подвижности
function updateMobilityCharacteristics(characteristics, ranges) {
  // Поддержка разных форматов данных скорости
  const forwardSpeed = typeof characteristics.speed === 'object' && characteristics.speed ? 
                      characteristics.speed.forward : characteristics.speed;
  const backwardSpeed = typeof characteristics.speed === 'object' && characteristics.speed ? 
                       characteristics.speed.reverse : characteristics.reverseSpeed;
  
  updateCharValue('speed', forwardSpeed, 'км/ч', ranges.speed);
  updateCharValue('reverseSpeed', backwardSpeed, 'км/ч', ranges.reverseSpeed);
  updateCharValue('enginePower', characteristics.enginePower, 'л.с.', ranges.enginePower);
  updateCharValue('powerToWeight', characteristics.powerToWeight, 'л.с./т', ranges.powerToWeight);
  updateCharValue('traverse', characteristics.traverse, '°/с', ranges.traverse);
}

// Обновление характеристик живучести
function updateSurvivabilityCharacteristics(characteristics, ranges) {
  updateCharValue('hitPoints', characteristics.hitPoints);
  updateCharValue('hullFront', characteristics.hullArmor?.front, 'мм', ranges.hullFront);
  updateCharValue('turretFront', characteristics.turretArmor?.front, 'мм', ranges.turretFront);
  updateCharValue('fireChance', characteristics.fireChance, '%', ranges.fireChance);
}

// Обновление характеристик обнаружения
function updateSpottingCharacteristics(characteristics, ranges) {
  updateCharValue('viewRange', characteristics.viewRange, 'м', ranges.viewRange);
  updateCharValue('radioRange', characteristics.radioRange, 'м', ranges.radioRange);
}

// Безопасное обновление значения характеристики
function updateCharValue(id, value, suffix = '', range = null) {
  const element = document.getElementById(id);
  if (!element) return;
  
  // Если значение отсутствует, показываем прочерк
  if (value === undefined || value === null) {
    element.textContent = '—';
    hideProgressBar(id);
    return;
  }
  
  // Форматируем значение
  const formattedValue = formatValue(value, suffix);
  element.textContent = formattedValue;
  
  // Обновляем прогресс-бар
  if (typeof value === 'number' && range) {
    updateProgressBar(id, value, range);
  } else {
    hideProgressBar(id);
  }
}

// Форматирование значения
function formatValue(value, suffix) {
  if (typeof value === 'number') {
    const formatted = value % 1 !== 0 ? value.toFixed(1) : value.toString();
    return suffix ? `${formatted} ${suffix}` : formatted;
  }
  return suffix ? `${value} ${suffix}` : value.toString();
}

// Обновление прогресс-бара
function updateProgressBar(id, value, range) {
  const fillElement = document.getElementById(`${id}Fill`);
  if (!fillElement) return;
  
  fillElement.style.display = 'block';
  
  // Вычисляем процент для заполнения
  const percentage = calculatePercentage(value, range);
  
  // Устанавливаем ширину с анимацией
  setTimeout(() => {
    fillElement.style.width = `${percentage}%`;
  }, 10);
  
  // Добавляем классы для различных значений
  updateProgressBarClass(fillElement, percentage);
}

// Вычисление процента для прогресс-бара
function calculatePercentage(value, range) {
  const min = range.min || 0;
  const max = range.max || 100;
  const inverse = range.inverse || false;
  
  let percentage;
  if (inverse) {
    percentage = Math.max(0, Math.min(100, ((max - value) / (max - min)) * 100));
  } else {
    percentage = Math.max(0, Math.min(100, ((value - min) / (max - min)) * 100));
  }
  
  return percentage;
}

// Обновление класса прогресс-бара
function updateProgressBarClass(element, percentage) {
  element.classList.remove('low', 'medium', 'high');
  
  if (percentage < 30) {
    element.classList.add('low');
  } else if (percentage < 70) {
    element.classList.add('medium');
  } else {
    element.classList.add('high');
  }
}

// Скрыть прогресс-бар
function hideProgressBar(id) {
  const fillElement = document.getElementById(`${id}Fill`);
  if (fillElement) {
    fillElement.style.width = '0%';
    fillElement.style.display = 'none';
  }
}

// Скрыть все прогресс-бары
function hideAllProgressBars() {
  const progressBars = document.querySelectorAll('[id$="Fill"]');
  progressBars.forEach(bar => {
    bar.style.width = '0%';
    bar.style.display = 'none';
  });
}

// Создание таблицы характеристик
export function createCharacteristicTable(title, charData) {
  const table = document.createElement('table');
  table.className = 'characteristic-table';
  
  const caption = table.createCaption();
  caption.textContent = title;
  
  const tbody = table.createTBody();
  
  charData.forEach(item => {
    if (item.value !== undefined && item.value !== null) {
      tbody.appendChild(createTableRow(item.label, item.value, item.unit));
    }
  });
  
  return tbody.children.length > 0 ? table : null;
}

// Создание строки таблицы
function createTableRow(label, value, unit = '') {
  const tr = document.createElement('tr');
  
  const tdValue = document.createElement('td');
  tdValue.className = 'char-value';
  tdValue.textContent = formatTableValue(value, unit);
  
  const tdLabel = document.createElement('td');
  tdLabel.className = 'char-name';
  tdLabel.textContent = label;
  
  tr.appendChild(tdValue);
  tr.appendChild(tdLabel);
  
  return tr;
}

// Форматирование значения для таблицы
function formatTableValue(value, unit) {
  if (typeof value === 'number') {
    return value.toLocaleString() + (unit ? ` ${unit}` : '');
  } else if (value !== null && value !== undefined) {
    return value + (unit ? ` ${unit}` : '');
  }
  return 'N/A';
}

// Обновление характеристик с учетом оборудования
export function updateCharacteristicsWithEquipment(baseCharacteristics, equipment) {
  if (!baseCharacteristics) return;
  
  // Копируем базовые характеристики
  const modifiedCharacteristics = { ...baseCharacteristics };
  
  // Применяем бонусы от оборудования
  if (equipment) {
    Object.values(equipment).forEach(item => {
      if (item && item.bonuses) {
        applyEquipmentBonuses(modifiedCharacteristics, item.bonuses);
      }
    });
  }
  
  // Обновляем отображение
  updateCharacteristics(modifiedCharacteristics);
  
  // Подсвечиваем измененные значения
  highlightModifiedValues(baseCharacteristics, modifiedCharacteristics);
}

// Применение бонусов от оборудования
function applyEquipmentBonuses(characteristics, bonuses) {
  Object.entries(bonuses).forEach(([stat, bonus]) => {
    if (characteristics[stat] !== undefined && typeof characteristics[stat] === 'number') {
      characteristics[stat] *= (1 + bonus);
    }
  });
}

// Подсветка измененных значений
function highlightModifiedValues(baseChars, modifiedChars) {
  Object.keys(baseChars).forEach(key => {
    const element = document.getElementById(key);
    if (!element) return;
    
    if (baseChars[key] !== modifiedChars[key]) {
      element.classList.add('modified-value');
      
      // Добавляем индикатор изменения
      const changeIndicator = document.createElement('span');
      changeIndicator.className = 'change-indicator';
      
      const change = modifiedChars[key] - baseChars[key];
      const isPositive = change > 0;
      
      // Для инвертированных характеристик (где меньше - лучше)
      const invertedStats = ['reloadTime', 'aimTime', 'dispersion', 'fireChance'];
      const isImprovement = invertedStats.includes(key) ? change < 0 : change > 0;
      
      changeIndicator.textContent = `(${isPositive ? '+' : ''}${change.toFixed(1)})`;
      changeIndicator.classList.add(isImprovement ? 'improvement' : 'degradation');
      
      // Удаляем старый индикатор, если есть
      const oldIndicator = element.querySelector('.change-indicator');
      if (oldIndicator) {
        oldIndicator.remove();
      }
      
      element.appendChild(changeIndicator);
    } else {
      element.classList.remove('modified-value');
      const indicator = element.querySelector('.change-indicator');
      if (indicator) {
        indicator.remove();
      }
    }
  });
}

// Экспорт дополнительных функций
export {
  hideAllProgressBars,
  updateProgressBar,
  calculatePercentage
};
