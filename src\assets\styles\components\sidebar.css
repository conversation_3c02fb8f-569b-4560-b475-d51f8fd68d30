/* Боковая панель в стиле Unity */

.sidebar-logo-icon {
  /* Header logo size - like Best21 */
  width: 24px;
  height: 24px;
  flex-shrink: 0;
  margin-right: 10px;
  display: block;
  object-fit: contain;
  /* Spin anchor */
  transform-origin: 50% 50%;
  /* Дыхание и подсветка как у текста, но для иконки */
  animation: glowPulse 2.6s ease-in-out infinite;
  /* Better vertical alignment */
  vertical-align: middle;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* Smooth transition */
  /* Removed cursor: pointer */
}

/* Убираем CSS hover анимацию, так как она управляется через JavaScript */

/* Эффект переливающихся шариков-волн */
.sidebar-logo-icon path:nth-child(1) {
  stroke: #ff2db3;
  animation: iconColorFlow1 4s ease-in-out infinite;
}
.sidebar-logo-icon path:nth-child(2) {
  stroke: #ffa020;
  animation: iconColorFlow2 4s ease-in-out infinite;
}
.sidebar-logo-icon path:nth-child(3) {
  stroke: #1eb83a;
  animation: iconColorFlow3 4s ease-in-out infinite;
}
.sidebar-logo-icon path:nth-child(4) {
  stroke: #0a7fff;
  animation: iconColorFlow4 4s ease-in-out infinite;
}

/* Дополнительные эффекты для супер-быстрого вращения */
.sidebar-logo-icon.super-fast-spin {
  transform-origin: 50% 50%;
  /* Более яркие цвета при супер-быстром вращении */
}

.sidebar-logo-icon.super-fast-spin path:nth-child(1) { stroke: #ff2db3; }
.sidebar-logo-icon.super-fast-spin path:nth-child(2) { stroke: #ffa020; }
.sidebar-logo-icon.super-fast-spin path:nth-child(3) { stroke: #1eb83a; }
.sidebar-logo-icon.super-fast-spin path:nth-child(4) { stroke: #0a7fff; }

/* Плавные анимации вращения в разные стороны */
@keyframes spinClockwise {
  to { transform: rotate(360deg); }
}

@keyframes spinCounterClockwise {
  to { transform: rotate(-360deg); }
}

@keyframes spinFast {
  to { transform: rotate(360deg); }
}

@keyframes spinSlow {
  to { transform: rotate(360deg); }
}

/* Плавная остановка с возвратом в исходное положение */
@keyframes spinToZero {
  0% { transform: rotate(var(--current-rotation, 0deg)); }
  100% { transform: rotate(0deg); }
}

/* Дополнительные анимации для плавной остановки */
@keyframes spinToZeroSlow {
  0% { transform: rotate(var(--current-rotation, 0deg)); }
  100% { transform: rotate(0deg); }
}

/* Старые анимации для совместимости */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes spin-out {
  from { transform: rotate(0deg); }
  to { transform: rotate(180deg); }
}

@keyframes spin360 {
  to { transform: rotate(360deg); }
}

@keyframes spin360Reverse {
  to { transform: rotate(-360deg); }
}

@keyframes glowPulse {
  0%,100% {
    filter: drop-shadow(0 0 12px rgba(255,255,255,0.9)) drop-shadow(0 0 20px rgba(255,255,255,0.7));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(255,255,255,1)) drop-shadow(0 0 28px rgba(255,255,255,0.9));
  }
}

@keyframes textPulse {
  0%,100% {
    text-shadow: 0 0 6px rgba(255,255,255,0.9), 0 0 16px rgba(172,142,255,0.65);
  }
  50% {
    text-shadow: 0 0 10px rgba(255,255,255,1), 0 0 24px rgba(172,142,255,0.9);
  }
}

/* Дыхание для AI Efficiency без влияния на градиент - УВЕЛИЧЕННАЯ НАСЫЩЕННОСТЬ */
@keyframes aiTextGlow {
  0%,100% {
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 20px rgba(255, 255, 255, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 16px rgba(255, 255, 255, 1)) drop-shadow(0 0 28px rgba(255, 255, 255, 0.8));
  }
}







/* Анимации переливающихся шариков-волн */
/* ГОРИЗОНТАЛЬНЫЙ ШАРИК: лево→право (розовый шарик) */
/* Path 1 (ЛЕВАЯ): шарик стартует здесь */
@keyframes iconColorFlow1 {
  0% { stroke: #ff2db3; }
  25% { stroke: #ff2db3; }
  50% { stroke: #1eb83a; }
  75% { stroke: #1eb83a; }
  100% { stroke: #ff2db3; }
}

/* Path 3 (ПРАВАЯ): шарик приходит сюда с задержкой */
@keyframes iconColorFlow3 {
  0% { stroke: #1eb83a; }
  12.5% { stroke: #1eb83a; }
  37.5% { stroke: #ff2db3; }
  62.5% { stroke: #ff2db3; }
  87.5% { stroke: #1eb83a; }
  100% { stroke: #1eb83a; }
}

/* ВЕРТИКАЛЬНЫЙ ШАРИК: верх→низ (желтый шарик) */
/* Path 2 (ВЕРХНЯЯ): шарик стартует здесь */
@keyframes iconColorFlow2 {
  0% { stroke: #ffa020; }
  25% { stroke: #ffa020; }
  50% { stroke: #0a7fff; }
  75% { stroke: #0a7fff; }
  100% { stroke: #ffa020; }
}

/* Path 4 (НИЖНЯЯ): шарик приходит сюда с задержкой */
@keyframes iconColorFlow4 {
  0% { stroke: #0a7fff; }
  12.5% { stroke: #0a7fff; }
  37.5% { stroke: #ffa020; }
  62.5% { stroke: #ffa020; }
  87.5% { stroke: #0a7fff; }
  100% { stroke: #0a7fff; }
}

/* RGB анимация цвета линии - плавная смена цветов */
@keyframes rgbLineColor {
  0% {
    border-bottom-color: rgba(255, 0, 100, 0.9);
  }
  16.66% {
    border-bottom-color: rgba(255, 100, 0, 0.9);
  }
  33.33% {
    border-bottom-color: rgba(255, 200, 0, 0.9);
  }
  50% {
    border-bottom-color: rgba(100, 255, 0, 0.9);
  }
  66.66% {
    border-bottom-color: rgba(0, 200, 255, 0.9);
  }
  83.33% {
    border-bottom-color: rgba(150, 0, 255, 0.9);
  }
  100% {
    border-bottom-color: rgba(255, 0, 100, 0.9);
  }
}

/* Empty .sidebar-header-content ruleset removed to fix lint error */

div.sidebar .sidebar-header-content { /* Increased specificity for header container */
  display: flex;
  flex-direction: row; /* Force horizontal layout */
  align-items: center;
  justify-content: center; /* Perfect center alignment */
  padding: 1.25rem 1rem; /* Equal padding on all sides */
  border-bottom: 2px solid rgba(255, 0, 100, 0.9); /* RGB линия */
  animation: rgbLineColor 4s linear infinite;
  margin-bottom: 5px; /* Minimal space before ANALYSIS */
  min-height: 60px; /* Ensure consistent height */
  gap: 0; /* Remove any gap between items */
  width: 100%; /* Full width for proper centering */
  position: relative; /* Для псевдоэлементов */
  overflow: visible; /* Показываем тень */
  /* Removed cursor: pointer and hover effects */
}

/* Тень под RGB линией - синхронизированная с цветом линии */
div.sidebar .sidebar-header-content::before {
  content: '';
  position: absolute;
  bottom: -3px; /* Ниже линии */
  left: 0;
  width: 100%;
  height: 2px;
  background: rgba(255, 0, 100, 0.4);
  animation: rgbShadowColor 4s linear infinite;
  filter: blur(1px);
  z-index: -1;
}

/* Анимация цвета тени - синхронизирована с линией */
@keyframes rgbShadowColor {
  0% {
    background: rgba(255, 0, 100, 0.4);
  }
  16.66% {
    background: rgba(255, 100, 0, 0.4);
  }
  33.33% {
    background: rgba(255, 200, 0, 0.4);
  }
  50% {
    background: rgba(100, 255, 0, 0.4);
  }
  66.66% {
    background: rgba(0, 200, 255, 0.4);
  }
  83.33% {
    background: rgba(150, 0, 255, 0.4);
  }
  100% {
    background: rgba(255, 0, 100, 0.4);
  }
}

/* Старые анимации удалены */

@keyframes neonShimmer {
  0% {
    left: -100%;
    opacity: 0;
  }
  10% {
    opacity: 0.8;
  }
  50% {
    left: 0%;
    opacity: 1;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}

.ai-title-text {
  font-weight: 600; /* Ensure semibold is applied */
  animation: aiTextGlow 2.6s ease-in-out infinite;
  font-size: 1rem; /* Slightly larger than ANALYSIS */
  background: linear-gradient(135deg, #ffffff, #f8fafc, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  white-space: nowrap; /* Prevent text wrapping */
  /* Removed cursor: pointer and hover effects */
}

/* Responsive behavior - ПОКАЗЫВАЕМ AI Efficiency на всех экранах */
@media (max-width: 767px) {
  div.sidebar .sidebar-header-content {
    justify-content: center; /* Center icon + text on mobile */
    padding: 1rem 0.75rem; /* Немного меньше отступы на мобильных */
  }

  .ai-title-text {
    display: flex; /* Показываем текст на мобильных */
    font-size: 0.9rem; /* Немного меньше размер на мобильных */
  }
}

@media (min-width: 768px) {
  div.sidebar .sidebar-header-content {
    justify-content: center; /* Center icon + text on desktop */
  }

  .ai-title-text {
    display: flex; /* Show text on desktop */
    font-size: 1rem; /* Полный размер на десктопе */
  }
}

.section-title {
  padding: 0rem 1.5rem 0.5rem 1.5rem; /* No top padding, only bottom */
  margin-bottom: 0rem;
  font-size: 0.7rem; /* Approx 11px */
  font-weight: 600; /* Semibold */
  text-transform: uppercase;
  color: rgba(226, 230, 240, 0.5); /* Muted text color for 'ANALYSIS' */
  opacity: 0.6;
  letter-spacing: 1px;
  border-bottom: none;
}

:root {
  --sidebar-width: 250px;
  --sidebar-bg: #1d1d2e;
  --sidebar-text: #e6e6e6;
  --sidebar-item-hover: rgba(154, 98, 230, 0.08);
  --sidebar-item-active: #7254e4;
  --sidebar-border: 1px solid rgba(61, 61, 90, 0.2);
  --sidebar-icon-color: #a17bf8;
  --sidebar-transition: all 0.2s ease;
  --unity-purple: #7254e4;
  --unity-purple-light: #a17bf8;
  --unity-dark: #27233d;
  --unity-darker: #1d1a30;
  --glass-highlight: rgba(255, 255, 255, 0.07);
  --glass-shadow: rgba(0, 0, 0, 0.1);
}

/* Дублирующиеся стили body удалены - используются из main.css */

/* Общий контейнер приложения */
.app-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  min-height: 100vh;
  position: relative;
  background-color: inherit;
  box-sizing: border-box;
  overflow-x: hidden;
}

.sidebar {
  width: 250px; /* Fixed width to prevent stretching */
  min-width: 250px; /* Prevent shrinking */
  max-width: 250px; /* Prevent growing */
  height: 100vh; /* Fixed height */
  background: linear-gradient(180deg,
    rgba(45, 35, 75, 0.75) 0%,
    rgba(35, 28, 60, 0.7) 100%);
  color: var(--sidebar-text);
  flex-shrink: 0;
  position: fixed; /* Fixed position to prevent rubber effect */
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
  overflow: hidden; /* No scrollbar */
  backdrop-filter: blur(20px) saturate(200%);
  -webkit-backdrop-filter: blur(20px) saturate(200%);
  border-right: 1.5px solid rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.08);
  isolation: isolate;
  transform: translateZ(0) scale(1); /* Prevent scaling */
  -webkit-transform: translateZ(0) scale(1);
  will-change: auto; /* Prevent transform changes */
  transition: none; /* No transitions to prevent rubber effect */
  z-index: 10;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  padding: 20px 24px; /* Увеличенный отступ для логотипа */
  margin-bottom: 5px;
  border-bottom: var(--sidebar-border);
  background: transparent;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background-color: var(--unity-purple-light);
  border-radius: 8px;
  margin-right: 12px;
  position: relative;
}

.logo-icon:before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50%;
  height: 50%;
  background-color: var(--sidebar-bg);
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
}

.logo-icon-brain {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
}

.logo-icon-brain svg {
  width: 100%;
  height: 100%;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 600;
  margin-left: 16px; /* Увеличиваем отступ между иконкой и текстом */
  transform: translateY(2px); /* Сдвигаем текст вниз для выравнивания по центру */
  color: white;
  text-shadow: 0px 0px 5px rgba(255, 255, 255, 0.2);
  letter-spacing: 0.5px;
}

.sidebar-section {
  margin-bottom: 24px;
  padding: 0 16px;
}

.flag-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0 1.5rem 0.5rem;
}

.flag-list img {
  width: 35px;
  height: 27px;
  object-fit: contain;
  border-radius: 2px;
}

/* Divider line between flag and type filter sections */
.flag-divider {
  width: 100%;
  height: 1px;
  background: rgba(255, 255, 255, 0.2);
  margin: 0;
}

/* Removed duplicate .section-title - using the one at line 465 instead */

.sidebar-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background: transparent;
  border: 1px solid transparent;
  position: relative;
  color: var(--sidebar-text);
  font-weight: 500;
  box-shadow: none;
  outline: none;
  text-shadow: none;
}

.sidebar-menu-item:hover {
  background: rgba(114, 84, 228, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(114, 84, 228, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Active menu item styles */
/* Active menu item styles */
.sidebar-menu-item.active {
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border-radius: 0.5rem;
  border: 1px solid var(--unity-purple-light); /* #a17bf8 */
  box-shadow: none;
}

.sidebar-menu-item .menu-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  color: var(--sidebar-icon-color, #a17bf8); /* Default icon color (light purple) */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-menu-item .menu-icon svg {
  width: 100%; /* Use 100% to fill the 20x20 container */
  height: 100%;
  stroke-width: 1.5; /* Consistent stroke width */
  stroke: #a17bf8;
  fill: none;
}

.sidebar-menu-item .menu-text {
  font-size: clamp(0.9rem, 1.4vw, 1rem); /* Увеличенный размер как у названий танков */
  line-height: 1.2;
  background: linear-gradient(135deg, #a17bf8, #8b5cf6, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600; /* Semibold weight for non-active text */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  filter: contrast(1.1) brightness(1.05);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-menu-item.active .menu-text {
  opacity: 1;
  font-weight: 600;
  color: #ffffff;
}

.sidebar-menu-item.active .menu-icon svg {
  stroke: #ffffff;
  fill: none;
}

.sidebar-menu-item.active .menu-text {
  background: linear-gradient(135deg, #ffffff, #f8fafc, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700; /* Более жирный шрифт для активной вкладки */
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
}

/* Hover эффекты для главных вкладок меню */
.sidebar-menu-item:hover .menu-text {
  background: linear-gradient(135deg, #ffffff, #f1f5f9, #e2e8f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 700;
}

/* Flag item button styles */
.flag-item {
  display: flex;
  align-items: center;
  gap: 6px; /* tighter gap */
  padding: 2px 8px; /* reduce vertical padding to offset larger icon */
  width: 100%;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px; /* Match main menu items */
  cursor: pointer;
  color: var(--sidebar-text);
  transition: background 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.flag-item:hover,
.flag-item:focus-visible {
  background: rgba(114, 84, 228, 0.15); /* Main menu hover background */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Main menu hover shadow */
  /* border-radius will be 8px from base .flag-item */
}

/* Removed hover effects for icons to maintain quality */

/* Selected country highlight */
.flag-item.selected,
.flag-item.active,
.flag-item:active { /* Added :active pseudo-class */
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border: 1px solid var(--unity-purple-light); /* #a17bf8 */
  box-shadow: none;
  color: #ffffff;                                 /* Ensure item text color is white */
  border-radius: 0.5rem;
}

.flag-item.selected .flag-name,
.flag-item.active .flag-name,
.flag-item:active .flag-name { /* Added :active for text */
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 700;
}

/* Hover эффект для названий стран */
.flag-item:hover .flag-name {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 700;
}

.flag-item img {
  flex-shrink: 0;
  width: 38px; /* Default size for large screens */
  height: 30px;
  object-fit: contain;
  border-radius: 2px;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
}

.flag-name {
  font-size: clamp(0.8rem, 1.4vw, 0.9rem);
  font-weight: 600;
  white-space: nowrap;
  color: var(--sidebar-icon-color, #a17bf8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  filter: contrast(1.1) brightness(1.05);
  transition: all 0.2s ease;
}

/* Tank type list styles */
.type-list {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem 1.5rem 0.75rem;
}

/* Type item button styles (similar to flag-item) */
.type-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 2px 8px;
  width: 100%;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px; /* Match main menu items */
  cursor: pointer;
  color: var(--sidebar-text);
  transition: background 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.type-item:hover,
.type-item:focus-visible {
  background: rgba(114, 84, 228, 0.15); /* Main menu hover background */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Main menu hover shadow */
  /* border-radius will be 8px from base .type-item */
}

.type-item.selected,
.type-item.active,
.type-item:active { /* Added :active pseudo-class */
  background: linear-gradient(135deg, rgba(114, 84, 228, 0.8), rgba(96, 70, 192, 0.9));
  border: 1px solid var(--unity-purple-light); /* #a17bf8 */
  box-shadow: none;
  color: #ffffff;                                 /* Ensure item text color is white */
  border-radius: 0.5rem;
}

.type-item.selected .type-name,
.type-item.active .type-name,
.type-item:active .type-name { /* Added :active for text */
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 700;
}

/* Hover эффект для названий типов техники */
.type-item:hover .type-name {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  filter: contrast(1.2) brightness(1.1);
  font-weight: 700;
}

.type-item img {
  flex-shrink: 0;
  width: 38px; /* Default size for large screens */
  height: 30px;
  object-fit: contain;
  border-radius: 2px;
  image-rendering: auto; /* Smooth, high-quality rendering */
}

.type-name {
  font-size: clamp(0.8rem, 1.4vw, 0.9rem);
  font-weight: 600;
  white-space: nowrap;
  color: var(--sidebar-icon-color, #a17bf8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  filter: contrast(1.1) brightness(1.05);
  transition: all 0.2s ease;
}

/* Animation for country/type filter container */
.flag-section {
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transform: translateY(-4px);
  transition: max-height 0.25s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.flag-section.open {
  max-height: 1000px; /* Large enough for all content */
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

/* HD Monitor optimizations (1920x1080 and below) */
@media (max-height: 1080px) {
  /* Compact spacing for HD screens */
  .sidebar-section {
    margin-bottom: 12px; /* Reduced spacing */
  }

  .flag-list {
    gap: 0.15rem; /* Tighter gaps */
    padding: 0 1.2rem 0.3rem;
  }

  .type-list {
    gap: 0.15rem; /* Tighter gaps */
    padding: 0.2rem 1.2rem 0.4rem;
  }

  .flag-item, .type-item {
    padding: 1px 6px; /* Compact padding */
  }

  .flag-item img, .type-item img {
    width: 34px; /* Optimized size for HD screens */
    height: 26px;
  }

  .flag-name, .type-name {
    font-size: clamp(0.75rem, 1.6vw, 0.85rem); /* Увеличенный размер */
  }
}

/* Medium screens (900px height and below) */
@media (max-height: 900px) {
  .sidebar-section {
    margin-bottom: 8px; /* Even more compact */
  }

  .flag-item, .type-item {
    padding: 0px 4px; /* Very compact */
  }

  .flag-item img, .type-item img {
    width: 30px; /* Optimized for medium screens */
    height: 23px;
  }

  .flag-name, .type-name {
    font-size: clamp(0.7rem, 1.8vw, 0.8rem); /* Увеличенный размер */
  }
}

/* Small screens (768px height and below) */
@media (max-height: 768px) {
  .sidebar-menu-item {
    padding: 6px 12px; /* Compact menu items */
    margin-bottom: 2px;
  }

  .sidebar-section {
    margin-bottom: 6px; /* Minimal spacing */
  }

  .flag-list, .type-list {
    gap: 0.1rem; /* Minimal gaps */
    padding: 0.1rem 1rem 0.2rem;
  }

  .flag-item, .type-item {
    padding: 0px 3px; /* Minimal padding */
  }

  .flag-item img, .type-item img {
    width: 26px; /* Small but clear icons */
    height: 20px;
  }

  .flag-name, .type-name {
    font-size: clamp(0.65rem, 2vw, 0.75rem); /* Увеличенный размер */
  }
}

.sidebar-menu-item:not(.active):focus,
.sidebar-menu-item:not(.active):active,
.sidebar-menu-item:not(.active):focus-visible {
  outline: none;
  outline-style: none;
  outline-offset: 0;
  outline-width: 0;
  box-shadow: none;
  --tw-ring-shadow: none;
  --tw-ring-offset-shadow: none;
  --tw-ring-color: transparent;
  --tw-ring-offset-width: 0;
}

/* Multicolor logo lines - ЯРКИЕ СОЧНЫЕ ЦВЕТА БЕЗ ТУСКЛОСТИ */
.sidebar-logo-icon path:nth-child(1) { stroke: #ff2db3; }
.sidebar-logo-icon path:nth-child(2) { stroke: #ffa020; }
.sidebar-logo-icon path:nth-child(3) { stroke: #1eb83a; }
.sidebar-logo-icon path:nth-child(4) { stroke: #0a7fff; }

.sidebar-section {
  margin-bottom: 24px;
  padding: 0 16px;
}

/* Removed duplicate .section-title - using the one at line 122 instead */

/* Removed duplicate .sidebar-menu-item styles - using the ones at line 280 instead */

