import { state } from '../../store/state.js';
import { STANDARD_EQUIPMENT, SPECIAL_EQUIPMENT, IMPROVED_EQUIPMENT, EXPERIMENTAL_EQUIPMENT } from '../../data/equipment.js';

// Константы для слотов оборудования
const EQUIPMENT_SLOTS = {
  1: { name: 'Слот 1', defaultImage: 'src/assets/images/equipment/EquipSlot1Mobility.png' },
  2: { name: 'Слот 2', defaultImage: 'src/assets/images/equipment/EquipSlot2.png' },
  3: { name: 'Слот 3', defaultImage: 'src/assets/images/equipment/EquipSlot3-4.png' },
  4: { name: 'Слот 4', defaultImage: 'src/assets/images/equipment/EquipSlot3-4.png' }
};

// Типы слотов для разных танков
const SLOT_TYPES = {
  mobility: 'src/assets/images/equipment/EquipSlot1Mobility.png',
  survival: 'src/assets/images/equipment/EquipSlot1Survive.png',
  default: 'src/assets/images/equipment/EquipSlot2.png',
  universal: 'src/assets/images/equipment/EquipSlot3-4.png'
};

// Создание элемента слота оборудования
function createEquipmentSlot(slotNumber, slotType = 'default') {
  const slot = document.createElement('div');
  slot.className = `equipment-slot equipment-slot-${slotNumber}`;
  slot.id = `equipment-slot-${slotNumber}`;
  slot.dataset.slot = slotNumber;

  // Устанавливаем фоновое изображение
  const slotImage = SLOT_TYPES[slotType] || EQUIPMENT_SLOTS[slotNumber].defaultImage;
  slot.style.backgroundImage = `url('${slotImage}')`;

  // Добавляем обработчик клика
  slot.addEventListener('click', () => handleSlotClick(slotNumber));

  // Добавляем подсказку
  const tooltip = document.createElement('div');
  tooltip.className = 'equipment-slot-tooltip';
  tooltip.textContent = EQUIPMENT_SLOTS[slotNumber].name;
  slot.appendChild(tooltip);

  return slot;
}

// Обработчик клика по слоту
function handleSlotClick(slotNumber) {
  // Открываем модальное окно выбора оборудования
  showEquipmentModal(slotNumber);
}

// Показать модальное окно выбора оборудования
function showEquipmentModal(slotNumber) {
  const modal = document.getElementById('equipment-modal');
  if (!modal) {
    console.error('Equipment modal not found');
    return;
  }

  // Обновляем заголовок модального окна
  const modalTitle = modal.querySelector('.modal-title');
  if (modalTitle) {
    modalTitle.textContent = `Выберите оборудование для ${EQUIPMENT_SLOTS[slotNumber].name}`;
  }

  // Загружаем список доступного оборудования
  renderAvailableEquipment(slotNumber);

  // Показываем модальное окно
  modal.classList.add('active');
  modal.style.display = 'flex';
}

// Отрисовка доступного оборудования
function renderAvailableEquipment(slotNumber) {
  const container = document.getElementById('equipment-list');
  if (!container) return;

  container.innerHTML = '';
  const fragment = document.createDocumentFragment();

  // Фильтруем оборудование по совместимости со слотом
  const compatibleEquipment = filterEquipmentBySlot(slotNumber);

  compatibleEquipment.forEach(equipment => {
    const item = createEquipmentItem(equipment, slotNumber);
    fragment.appendChild(item);
  });

  container.appendChild(fragment);
}

// Создание элемента оборудования
function createEquipmentItem(equipment, slotNumber) {
  const item = document.createElement('div');
  item.className = 'equipment-item';
  item.dataset.equipmentId = equipment.id;

  // Иконка
  const icon = document.createElement('img');
  icon.className = 'equipment-icon';
  icon.src = equipment.icon || 'src/assets/images/equipment/default.png';
  icon.alt = equipment.name;

  // Информация
  const info = document.createElement('div');
  info.className = 'equipment-info';

  const name = document.createElement('h4');
  name.className = 'equipment-name';
  name.textContent = equipment.name;

  const description = document.createElement('p');
  description.className = 'equipment-description';
  description.textContent = equipment.description || '';

  // Бонусы
  const bonuses = document.createElement('div');
  bonuses.className = 'equipment-bonuses';
  bonuses.innerHTML = formatEquipmentBonuses(equipment.bonuses);

  info.appendChild(name);
  info.appendChild(description);
  info.appendChild(bonuses);

  item.appendChild(icon);
  item.appendChild(info);

  // Обработчик выбора
  item.addEventListener('click', () => selectEquipment(equipment, slotNumber));

  return item;
}

// Форматирование бонусов оборудования
function formatEquipmentBonuses(bonuses) {
  if (!bonuses || typeof bonuses !== 'object') return '';

  // Обрабатываем base или category бонусы
  const actualBonuses = bonuses.base || bonuses.category || bonuses;

  return Object.entries(actualBonuses)
    .map(([key, value]) => {
      if (typeof value === 'object') return ''; // Пропускаем сложные объекты
      const sign = value > 0 ? '+' : '';
      const formattedValue = typeof value === 'number' ?
        `${(value * 100).toFixed(1)}%` : value;
      return `<span class="bonus-item">${sign}${formattedValue} ${translateBonusKey(key)}</span>`;
    })
    .filter(Boolean)
    .join('<br>');
}

// Перевод ключей бонусов
function translateBonusKey(key) {
  const translations = {
    reloadTime: 'к перезарядке',
    aimTime: 'к сведению',
    dispersion: 'к разбросу',
    viewRange: 'к обзору',
    crewBonus: 'к навыкам экипажа',
    speed: 'к скорости',
    traverse: 'к повороту',
    hitPoints: 'к прочности',
    enginePower: 'к мощности двигателя',
    speedForward: 'к макс. скорости',
    speedReverse: 'к скорости заднего хода',
    hullDurability: 'к прочности корпуса',
    trackDurability: 'к прочности гусениц',
    repairSpeed: 'к скорости ремонта',
    camouflage: 'к маскировке',
    moduleProtection: 'к защите модулей'
  };

  return translations[key] || key;
}

// Фильтрация оборудования по слоту
function filterEquipmentBySlot(_slotNumber) {
  // Объединяем все типы оборудования
  const allEquipment = {
    ...STANDARD_EQUIPMENT,
    ...SPECIAL_EQUIPMENT,
    ...IMPROVED_EQUIPMENT,
    ...EXPERIMENTAL_EQUIPMENT
  };

  // Преобразуем объект в массив
  return Object.entries(allEquipment).map(([name, data]) => ({
    id: name,
    name: name,
    ...data
  }));
}

// Выбор оборудования
function selectEquipment(equipment, slotNumber) {
  // Сохраняем выбор в состоянии
  if (!state.selectedEquipment) {
    state.selectedEquipment = {};
  }
  state.selectedEquipment[slotNumber] = equipment;

  // Обновляем визуальное отображение слота
  updateSlotVisual(slotNumber, equipment);

  // Закрываем модальное окно
  closeEquipmentModal();

  // Обновляем характеристики танка
  updateTankCharacteristics();
}

// Обновление визуального отображения слота
function updateSlotVisual(slotNumber, equipment) {
  const slot = document.getElementById(`equipment-slot-${slotNumber}`);
  if (!slot) return;

  // Очищаем слот
  slot.innerHTML = '';

  if (equipment) {
    // Добавляем иконку выбранного оборудования
    const icon = document.createElement('img');
    icon.className = 'selected-equipment-icon';
    icon.src = equipment.icon;
    icon.alt = equipment.name;

    // Добавляем кнопку удаления
    const removeBtn = document.createElement('button');
    removeBtn.className = 'remove-equipment-btn';
    removeBtn.innerHTML = '×';
    removeBtn.onclick = (e) => {
      e.stopPropagation();
      removeEquipment(slotNumber);
    };

    slot.appendChild(icon);
    slot.appendChild(removeBtn);
    slot.classList.add('has-equipment');
  } else {
    // Возвращаем стандартный вид слота
    slot.classList.remove('has-equipment');
    slot.style.backgroundImage = `url('${EQUIPMENT_SLOTS[slotNumber].defaultImage}')`;
  }
}

// Удаление оборудования из слота
function removeEquipment(slotNumber) {
  if (state.selectedEquipment) {
    delete state.selectedEquipment[slotNumber];
  }

  updateSlotVisual(slotNumber, null);
  updateTankCharacteristics();
}

// Закрытие модального окна
function closeEquipmentModal() {
  const modal = document.getElementById('equipment-modal');
  if (modal) {
    modal.classList.remove('active');
    modal.style.display = 'none';
  }
}

// Обновление характеристик танка с учетом оборудования
function updateTankCharacteristics() {
  // Эта функция будет вызывать обновление характеристик
  // с учетом выбранного оборудования
  if (window.updateCharacteristicsWithEquipment) {
    window.updateCharacteristicsWithEquipment();
  }
}

// Инициализация слотов оборудования
export function initializeEquipmentSlots(container, tankData) {
  if (!container) return;

  container.innerHTML = '';
  const fragment = document.createDocumentFragment();

  // Определяем типы слотов для конкретного танка
  const slotTypes = getSlotTypesForTank(tankData);

  // Создаем 4 слота
  for (let i = 1; i <= 4; i++) {
    const slotType = slotTypes[i] || 'default';
    const slot = createEquipmentSlot(i, slotType);
    fragment.appendChild(slot);
  }

  container.appendChild(fragment);
}

// Определение типов слотов для танка
function getSlotTypesForTank(tankData) {
  const slotTypes = {
    1: 'default',
    2: 'default',
    3: 'universal',
    4: 'universal'
  };

  // Определяем тип первого слота на основе класса танка
  if (tankData && tankData.type) {
    switch (tankData.type) {
      case 'LT':
      case 'MT':
        slotTypes[1] = 'mobility';
        break;
      case 'HT':
      case 'TD':
        slotTypes[1] = 'survival';
        break;
      default:
        slotTypes[1] = 'default';
    }
  }

  return slotTypes;
}

// Экспорт функций
export {
  createEquipmentSlot,
  showEquipmentModal,
  selectEquipment,
  removeEquipment,
  updateSlotVisual
};
