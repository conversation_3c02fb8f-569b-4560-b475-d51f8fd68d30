/* ======================================== 
   TANK LIST & CARDS
   ======================================== */

/* Tank list container */
#tank-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(208px, 1fr));
    gap: 0.75rem;
    padding: 0;
    margin: 4rem 1.5rem 0 0;
    overflow: visible;
    scrollbar-width: none;
}

#tank-list::-webkit-scrollbar {
    display: none;
}

/* Individual tank card */
.tank-item {
    background: transparent;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
    border: 1px solid transparent;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
    padding: 0.65rem;
    display: flex;
    flex-direction: row;
    align-items: stretch;
    gap: 0.65rem;
    cursor: pointer;
    transition: transform 0.25s ease, box-shadow 0.25s ease, background 0.25s ease, backdrop-filter 0.25s ease, border-color 0.25s ease;
    animation: tankCardIn 0.4s cubic-bezier(0.22, 1, 0.36, 1) both;
    will-change: transform, box-shadow, background, backdrop-filter;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    min-height: 90px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

/* Card entrance animation */
@keyframes tankCardIn {
    0% {
        opacity: 0;
        transform: translateY(12px) scale(0.96);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.tank-item:hover {
    transform: translateY(-8px) scale(1.03) rotateX(2deg);
    box-shadow: 0 14px 28px rgba(0, 0, 0, 0.28), 0 0 10px rgba(255, 255, 255, 0.16);
    background: rgba(255, 255, 255, 0.22);
    backdrop-filter: blur(6px) saturate(160%) brightness(1.12);
    -webkit-backdrop-filter: blur(6px) saturate(160%) brightness(1.12);
    border-color: transparent;
}

.tank-item:hover .tank-flag-background {
    filter: brightness(1.2) saturate(1.6);
    transform: scale(1.05);
    transition: filter 0.3s ease, transform 0.3s ease;
}

/* Selected state */
.tank-item.selected {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px var(--accent-color);
}

/* Category-specific selected highlight */
.tank-item.selected-lt { border-color: var(--color-lt); box-shadow: 0 0 0 2px var(--color-lt); }
.tank-item.selected-st { border-color: var(--color-st); box-shadow: 0 0 0 2px var(--color-st); }
.tank-item.selected-tt { border-color: var(--color-tt); box-shadow: 0 0 0 2px var(--color-tt); }
.tank-item.selected-td { border-color: var(--color-td); box-shadow: 0 0 0 2px var(--color-td); }
.tank-item.selected-spg { border-color: var(--color-spg); box-shadow: 0 0 0 2px var(--color-spg); }

/* Container for tank type badge + small icon */
.tank-type-container {
    display: flex;
    align-items: center;
    gap: 0;
    z-index: 2;
    width: 100%;
    min-width: 0;
}

/* Compact badge with tank type abbreviation */
.tank-type-indicator {
    font-size: 0.85rem;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 6px;
    line-height: 1;
    color: #ffffff;
    text-transform: uppercase;
    white-space: nowrap;
    flex-shrink: 0;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

/* Красивые затемненные градиенты на основе оригинальных цветов */
.tank-type-indicator.lt {
    background: linear-gradient(135deg, #7c3aed, #8b5cf6, #6d28d9);
    border: 1px solid rgba(124, 58, 237, 0.5);
}

.tank-type-indicator.st {
    background: linear-gradient(135deg, #db2777, #ec4899, #be185d);
    border: 1px solid rgba(219, 39, 119, 0.5);
}

.tank-type-indicator.tt {
    background: linear-gradient(135deg, #2563eb, #3b82f6, #1d4ed8);
    border: 1px solid rgba(37, 99, 235, 0.5);
}

.tank-type-indicator.td {
    background: linear-gradient(135deg, #059669, #10b981, #047857);
    border: 1px solid rgba(5, 150, 105, 0.5);
}

.tank-type-indicator.spg {
    background: linear-gradient(135deg, #dc2626, #ef4444, #b91c1c);
    border: 1px solid rgba(220, 38, 38, 0.5);
}

/* Hover эффекты для бейджиков */
.tank-type-indicator:hover {
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2);
    filter: brightness(1.1) saturate(1.1);
}

/* Small icon immediately following the badge */
.tank-type-icon {
    width: 30px;
    height: 30px;
    object-fit: contain;
    flex-shrink: 0;
    order: 1;
    margin-left: -6px;
}

/* Blurred country flag background */
.tank-flag-background {
    position: absolute;
    inset: -1.7rem;
    margin-left: -0.5rem;
    background-size: cover;
    background-position: center;
    filter: brightness(1) saturate(1.3);
    transition: filter 0.25s ease, transform 0.25s ease;
    z-index: 0;
    border-radius: inherit;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-font-smoothing: antialiased;
}

/* Ensure main tank icon uses background image correctly */
.tank-icon {
    width: 85px; /* Размер контейнера остается прежним */
    height: 85px; /* Размер контейнера остается прежним */
    background-size: 115%; /* Увеличиваем только изображение на 10% */
    background-position: center;
    background-repeat: no-repeat;
    z-index: 2;
    margin-left: auto; /* Возвращаем auto для правильного позиционирования */
    flex: 0 0 80px; /* Размер в flex остается прежним */
    order: 2;
    overflow: visible; /* Позволяем изображению выходить за границы */
    transform: translateX(-50px); /* Сдвигаем иконку левее на 3px */
}

/* Vertical information container inside the card */
.tank-info-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 0.3rem;
    z-index: 3;
    flex: 1 1 auto;
    order: 1;
    min-width: 0;
    height: 100%;
}

/* Optional: name row wrapper */
.tank-name-row {
    display: flex;
    justify-content: flex-start;
    width: auto;
    margin-top: auto;
    align-self: flex-start;
}

.tank-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.tank-name {
    font-size: clamp(0.8rem, 1.4vw, 0.9rem);
    font-weight: 700;
    text-align: left;
    background: linear-gradient(135deg, #ffffff, #ffffff, #f1f5f9);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: normal;
    font-stretch: condensed;
    letter-spacing: -0.01em;
    max-width: 100%;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    filter: contrast(1.1) brightness(1.05);
    transition: all 0.2s ease;
}

/* Улучшенная читаемость при hover с ярким градиентом */
.tank-item:hover .tank-name {
    background: linear-gradient(135deg, #ffffff, #ffffff, #f8fafc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    filter: contrast(1.2) brightness(1.2);
    font-weight: 700;
}

.tank-category,
.tank-role {
    font-size: 0.75rem;
    color: var(--gm-dark-text-secondary);
    letter-spacing: 0.03em;
}

/* Адаптивные размеры для мобильных устройств */
@media (max-width: 768px) {
    .tank-name {
        font-size: clamp(0.75rem, 1.6vw, 0.85rem);
    }
}

@media (max-width: 480px) {
    .tank-name {
        font-size: clamp(0.7rem, 1.8vw, 0.8rem);
    }
}
