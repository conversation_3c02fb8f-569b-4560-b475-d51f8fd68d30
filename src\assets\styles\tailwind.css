@tailwind base;
@tailwind components;
@tailwind utilities;

/* Кастомные компоненты с DaisyUI - правильный интерфейс */
@layer components {
  /* Базовая таблица без цветов - цвета задаются в tank-characteristics.css */
  .builds-table-daisy {
    @apply table w-full bg-transparent rounded-none;
    overflow: visible;
  }

  .builds-table-daisy thead {
    @apply bg-transparent text-white;
  }

  .builds-table-daisy th {
    @apply text-xs font-bold uppercase tracking-wide cursor-pointer bg-transparent transition-all duration-300;
  }

  .builds-table-daisy th:hover {
    @apply -translate-y-0.5 bg-transparent;
  }

  .builds-table-daisy td {
    @apply text-xs text-center bg-transparent;
  }

  .builds-table-daisy td:first-child {
    @apply text-left font-semibold;
  }

  /* Карточки примечаний с DaisyUI */
  .note-card-daisy {
    @apply card bg-base-200 shadow-xl border border-base-300 transition-all duration-300;
  }

  .note-card-daisy:hover {
    @apply -translate-y-1 border-accent shadow-2xl;
  }

  /* Кнопки с DaisyUI стилем */
  .btn-tank {
    @apply btn btn-primary btn-sm gap-2 transition-all duration-300;
  }

  .btn-tank:hover {
    @apply scale-105 brightness-110;
  }
}

/* Кастомные утилиты */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
  
  .glass-effect {
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
  
  .glow-primary {
    box-shadow: 0 0 20px rgba(114, 84, 228, 0.5);
  }
  
  .glow-accent {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
  }
  
  .no-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
}
