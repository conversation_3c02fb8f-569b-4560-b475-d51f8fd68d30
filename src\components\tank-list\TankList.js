// Removed unused imports: matchesFilters, getFilteredTanks, CompareService
import {
  getTankIconPath,
  getBlurredFlagPath,
  getTankTypeClass
} from '../../utils/constants.js';

// Соответствие типов танков и названий файлов иконок
const TANK_TYPE_TO_ICON = {
    'ЛТ': 'lightTank.png',
    'СТ': 'mediumTank.png',
    'ТТ': 'heavyTank.png',
    'ПТ-САУ': 'AT-SPG.png',
    'САУ': 'SPG.png'
};

// Виртуализация для больших списков танков
const VIRTUAL_SCROLL_THRESHOLD = 1000; // Временно увеличиваем порог для отладки
const ITEM_HEIGHT = 120; // Примерная высота элемента танка
const BUFFER_SIZE = 10; // Дополнительные элементы для плавности

export function renderTankList(tanks, tanksData, handleTankSelection, selectedTankName) {
    const tankListContainer = document.getElementById('tank-list');
    if (!tankListContainer) return;

    // Используем виртуализацию для больших списков
    if (tanks.length > VIRTUAL_SCROLL_THRESHOLD) {
        renderVirtualizedTankList(tanks, tankListContainer, handleTankSelection, selectedTankName);
        return;
    }

    // Обычный рендеринг для небольших списков
    renderRegularTankList(tanks, tankListContainer, handleTankSelection, selectedTankName);
}

function renderRegularTankList(tanks, tankListContainer, handleTankSelection, selectedTankName) {
    tankListContainer.innerHTML = '';
    const fragment = document.createDocumentFragment();

    tanks.forEach((tank, _index) => {
        const item = document.createElement('div');
        item.className = 'tank-item';

        // Добавляем начальное состояние для анимации
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';
        item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

        // Создаем контейнер для информации о танке - вертикальная структура
        const infoContainer = document.createElement('div');
        infoContainer.className = 'tank-info-container';

        // Создаем контейнер для бейджика и иконки рядом с ним
        const typeContainer = document.createElement('div');
        typeContainer.className = 'tank-type-container';

        // Создаем индикатор типа техники (бейджик)
        const typeIndicator = document.createElement('div');
        const suffix = getTankTypeClass(tank.type);
        typeIndicator.className = `tank-type-indicator ${suffix ? suffix : ''}`;
        typeIndicator.textContent = tank.type;

        // Добавляем иконку типа техники
        const typeIcon = document.createElement('img');
        typeIcon.className = 'tank-type-icon';
        typeIcon.src = `src/assets/images/upgrades/${TANK_TYPE_TO_ICON[tank.type]}`;
        typeIcon.alt = tank.type;

        // Добавляем бейджик и иконку в контейнер типа
        typeContainer.appendChild(typeIndicator);
        typeContainer.appendChild(typeIcon);

        // Создаем строку для названия
        const nameRow = document.createElement('div');
        nameRow.className = 'tank-name-row';

        // Создаем элемент фонового флага
        const flagBackground = document.createElement('div');
        flagBackground.className = 'tank-flag-background';

        // Используем функцию из constants.js для получения пути к размытому флагу
        const flagPath = getBlurredFlagPath(tank.country);
        flagBackground.style.backgroundImage = `url('${flagPath}')`;

        // Создаем элемент изображения танка
        const tankIcon = document.createElement('div');
        tankIcon.className = 'tank-icon';

        // Используем функцию из constants.js для получения пути к иконке танка
        const iconPath = getTankIconPath(tank.name);
        tankIcon.style.backgroundImage = `url('${iconPath}')`;

        // Создаем название танка
        const nameSpan = document.createElement('span');
        nameSpan.className = 'tank-name';
        nameSpan.textContent = tank.name;

        // Добавляем название в строку
        nameRow.appendChild(nameSpan);

        // Добавляем фоновый флаг в самом начале, чтобы он был под всеми остальными элементами
        item.appendChild(flagBackground);

        // Добавляем иконку танка поверх флага
        item.appendChild(tankIcon);

        // Добавляем элементы в контейнер в правильном порядке
        infoContainer.appendChild(typeContainer); // Сначала контейнер типа с бейджиком и иконкой
        infoContainer.appendChild(nameRow);    // Затем строка с названием

        // Добавляем контейнер информации в элемент танка
        item.appendChild(infoContainer);

        // Устанавливаем имя танка в датасет
        item.dataset.tankName = tank.name;

        // Выделяем выбранный танк
        if (tank.name === selectedTankName) {
            item.classList.add('selected');
            if (suffix) item.classList.add(`selected-${suffix}`);
        }

        // Добавляем обработчик клика
        if (handleTankSelection) {
            item.addEventListener('click', () => {
                handleTankSelection(tank.name);
            });
        }

        fragment.appendChild(item);
    });

    tankListContainer.appendChild(fragment);

    // Запускаем анимацию появления танков с задержкой
    const tankItems = tankListContainer.querySelectorAll('.tank-item');
    tankItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 50); // Задержка 50мс между танками для волнового эффекта
    });
}

// Виртуализированный рендеринг для больших списков
function renderVirtualizedTankList(tanks, tankListContainer, handleTankSelection, selectedTankName) {
    console.log(`[Virtualization] Rendering ${tanks.length} tanks with virtual scrolling`);

    // Очищаем контейнер
    tankListContainer.innerHTML = '';
    tankListContainer.style.position = 'relative';

    // Создаем виртуальный контейнер
    const virtualContainer = document.createElement('div');
    virtualContainer.className = 'virtual-scroll-container';
    virtualContainer.style.height = `${tanks.length * ITEM_HEIGHT}px`;
    virtualContainer.style.position = 'relative';

    // Создаем видимый контейнер
    const visibleContainer = document.createElement('div');
    visibleContainer.className = 'visible-items-container';
    visibleContainer.style.position = 'absolute';
    visibleContainer.style.top = '0';
    visibleContainer.style.width = '100%';

    virtualContainer.appendChild(visibleContainer);
    tankListContainer.appendChild(virtualContainer);

    // Removed unused variable: lastScrollTop
    let isScrolling = false;

    // Функция обновления видимых элементов
    const updateVisibleItems = () => {
        const scrollTop = tankListContainer.scrollTop;
        const containerHeight = tankListContainer.clientHeight;

        const startIndex = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - BUFFER_SIZE);
        const endIndex = Math.min(tanks.length, Math.ceil((scrollTop + containerHeight) / ITEM_HEIGHT) + BUFFER_SIZE);

        // Очищаем видимый контейнер
        visibleContainer.innerHTML = '';

        // Создаем фрагмент для батчинга DOM операций
        const fragment = document.createDocumentFragment();

        for (let i = startIndex; i < endIndex; i++) {
            const tank = tanks[i];
            const item = createTankItem(tank, selectedTankName, handleTankSelection);
            item.style.position = 'absolute';
            item.style.top = `${i * ITEM_HEIGHT}px`;
            item.style.width = '100%';

            // Добавляем анимацию появления для виртуализированных элементов
            item.style.opacity = '0';
            item.style.transform = 'translateY(10px)';
            item.style.transition = 'opacity 0.2s ease, transform 0.2s ease';

            fragment.appendChild(item);
        }

        visibleContainer.appendChild(fragment);

        // Запускаем анимацию для виртуализированных элементов
        const newItems = visibleContainer.querySelectorAll('.tank-item');
        newItems.forEach((item, index) => {
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 20); // Более быстрая анимация для виртуализированного списка
        });

        console.log(`[Virtualization] Rendered items ${startIndex}-${endIndex} of ${tanks.length}`);
    };

    // Throttled scroll handler
    const handleScroll = () => {
        if (!isScrolling) {
            requestAnimationFrame(() => {
                updateVisibleItems();
                isScrolling = false;
            });
            isScrolling = true;
        }
    };

    // Добавляем обработчик скролла
    tankListContainer.addEventListener('scroll', handleScroll, { passive: true });

    // Первоначальный рендеринг
    updateVisibleItems();
}

// Создание элемента танка (вынесено в отдельную функцию для переиспользования)
function createTankItem(tank, selectedTankName, handleTankSelection) {
    const item = document.createElement('div');
    item.className = 'tank-item';

    // Создаем контейнер для информации о танке - вертикальная структура
    const infoContainer = document.createElement('div');
    infoContainer.className = 'tank-info-container';

    // Создаем контейнер для бейджика и иконки рядом с ним
    const typeContainer = document.createElement('div');
    typeContainer.className = 'tank-type-container';

    // Создаем индикатор типа техники (бейджик)
    const typeIndicator = document.createElement('div');
    const suffix = getTankTypeClass(tank.type);
    typeIndicator.className = `tank-type-indicator ${suffix ? suffix : ''}`;
    typeIndicator.textContent = tank.type;

    // Добавляем иконку типа техники
    const typeIcon = document.createElement('img');
    typeIcon.className = 'tank-type-icon';
    typeIcon.src = `src/assets/images/upgrades/${TANK_TYPE_TO_ICON[tank.type]}`;
    typeIcon.alt = tank.type;

    // Добавляем бейджик и иконку в контейнер типа
    typeContainer.appendChild(typeIndicator);
    typeContainer.appendChild(typeIcon);

    // Создаем строку для названия
    const nameRow = document.createElement('div');
    nameRow.className = 'tank-name-row';

    // Создаем элемент фонового флага
    const flagBackground = document.createElement('div');
    flagBackground.className = 'tank-flag-background';

    // Используем функцию из constants.js для получения пути к размытому флагу
    const flagPath = getBlurredFlagPath(tank.country);
    flagBackground.style.backgroundImage = `url('${flagPath}')`;

    // Создаем элемент изображения танка
    const tankIcon = document.createElement('div');
    tankIcon.className = 'tank-icon';

    // Используем функцию из constants.js для получения пути к иконке танка
    const iconPath = getTankIconPath(tank.name);
    tankIcon.style.backgroundImage = `url('${iconPath}')`;

    // Создаем название танка
    const nameSpan = document.createElement('span');
    nameSpan.className = 'tank-name';
    nameSpan.textContent = tank.name;

    // Добавляем название в строку
    nameRow.appendChild(nameSpan);

    // Добавляем фоновый флаг в самом начале, чтобы он был под всеми остальными элементами
    item.appendChild(flagBackground);

    // Добавляем иконку танка поверх флага
    item.appendChild(tankIcon);

    // Добавляем элементы в контейнер в правильном порядке
    infoContainer.appendChild(typeContainer); // Сначала контейнер типа с бейджиком и иконкой
    infoContainer.appendChild(nameRow);    // Затем строка с названием

    // Добавляем контейнер информации в элемент танка
    item.appendChild(infoContainer);

    // Устанавливаем имя танка в датасет
    item.dataset.tankName = tank.name;

    // Выделяем выбранный танк
    if (tank.name === selectedTankName) {
        item.classList.add('selected');
        if (suffix) item.classList.add(`selected-${suffix}`);
    }

    // Добавляем обработчик клика
    if (handleTankSelection) {
        item.addEventListener('click', () => {
            handleTankSelection(tank.name);
        });
    }

    return item;
}

export function updateFilterSelection(filterType, targetValue) {
    const filterContainerId = filterType === 'country' ? 'nation-filter' : 'type-filter';
    const dataAttribute = filterType === 'country' ? 'country' : 'category';

    const filterItems = document.querySelectorAll(`#${filterContainerId} .filter-item`);

    filterItems.forEach(item => {
        const itemValue = item.dataset[dataAttribute];

        // Direct comparison: Highlight the item whose data attribute matches the target value
        if (itemValue === targetValue) {
            item.classList.add('selected');
            if (filterType === 'category') {
                const suffix = getTankTypeClass(itemValue);
                if (suffix) item.classList.add(`selected-${suffix}`);
            }
        } else {
            item.classList.remove('selected');
            if (filterType === 'category') {
                const suffix = getTankTypeClass(itemValue);
                if (suffix) item.classList.remove(`selected-${suffix}`);
            }
        }
    });
}
