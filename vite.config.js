import { defineConfig } from 'vite'
import { resolve } from 'path'

export default defineConfig({
  server: {
    port: 3000,
    host: true,
    open: true,
    strictPort: true // Принудительно использовать порт 3000
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'esbuild',
    target: 'es2020',
    rollupOptions: {
      output: {
        manualChunks: {
          'tank-data': ['./src/data/tanks.js'],
          'equipment-data': ['./src/data/equipment.js'],
          'ui-components': [
            './src/components/tank-list/TankList.js',
            './src/components/tank-details/TankDetails.js',
            './src/components/builds/BuildsTable.js'
          ],
          'services': [
            './src/services/SearchService.js',
            './src/services/FilterService.js',
            './src/services/TankAnalyzer.js'
          ]
        },
        // Оптимизация имен файлов
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    chunkSizeWarningLimit: 1000,
    // Дополнительные оптимизации
    cssCodeSplit: true,
    reportCompressedSize: false,
    // Предварительная загрузка критических ресурсов
    modulePreload: {
      polyfill: false
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@assets': resolve(__dirname, 'src/assets'),
      '@data': resolve(__dirname, 'src/data'),
      '@services': resolve(__dirname, 'src/services')
    }
  },
  optimizeDeps: {
    // Исключаем большие модули данных из предварительной оптимизации
    exclude: ['@data/tanks', '@data/equipment'],
    // Включаем часто используемые модули для предварительной оптимизации
    include: ['./src/utils/helpers.js', './src/services/FilterService.js'],
    // Оптимизация для лучшей производительности
    esbuildOptions: {
      target: 'es2020',
      // Удаляем console.log и debugger в продакшене
      drop: process.env.NODE_ENV === 'production' ? ['console', 'debugger'] : [],
      // Минификация для лучшей производительности
      minify: process.env.NODE_ENV === 'production',
      // Оптимизация для современных браузеров
      format: 'esm'
    }
  },
  // CSS оптимизации
  css: {
    devSourcemap: false,
    // Минификация CSS в продакшене
    postcss: {
      plugins: process.env.NODE_ENV === 'production' ? [
        require('cssnano')({
          preset: ['default', {
            discardComments: { removeAll: true },
            normalizeWhitespace: true,
            mergeLonghand: true,
            mergeRules: true
          }]
        })
      ] : [],
    },
    preprocessorOptions: {
      css: {
        charset: false
      }
    }
  },
  // Экспериментальные оптимизации
  experimental: {
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `/${filename}` }
      } else {
        return { relative: true }
      }
    }
  }
})
