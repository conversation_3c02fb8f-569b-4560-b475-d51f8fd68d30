/**
 * Test setup configuration
 * Настройка тестовой среды
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';

// Мокаем глобальные объекты браузера
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
});

// Мокаем IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Мокаем ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Мокаем requestAnimationFrame
global.requestAnimationFrame = (callback) => {
  return setTimeout(callback, 16);
};

global.cancelAnimationFrame = (id) => {
  clearTimeout(id);
};

// Мокаем performance API
global.performance = {
  now: () => Date.now(),
  memory: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000
  }
};

// Настройка DOM для каждого теста
beforeEach(() => {
  document.body.innerHTML = '';
  
  // Создаем базовую структуру DOM
  const app = document.createElement('div');
  app.id = 'app';
  document.body.appendChild(app);
});

// Очистка после каждого теста
afterEach(() => {
  document.body.innerHTML = '';
  
  // Очищаем все таймеры
  jest.clearAllTimers();
  
  // Очищаем все моки
  jest.clearAllMocks();
});

// Глобальная настройка перед всеми тестами
beforeAll(() => {
  // Устанавливаем тестовую среду
  process.env.NODE_ENV = 'test';
});

// Очистка после всех тестов
afterAll(() => {
  // Финальная очистка
});
