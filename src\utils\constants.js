// Общие константы для всего проекта

// Константы для типов танков
export const TANK_TYPE_TO_CLASS_SUFFIX = {
  'ЛТ': 'lt',
  'СТ': 'st',
  'ТТ': 'tt',
  'ПТ-САУ': 'td',
  'САУ': 'spg'
};

// Соответствие стран и алиасов для изображений
export const COUNTRY_IMAGE_ALIAS = {
  'international': 'intunion'
};

// Карта соответствия названий танков и файлов иконок
export const TANK_NAME_TO_ICON_MAP = {
  // USSR
  "Т-100 ЛТ": "t-100-lt",
  "Объект 907": "object-907",
  "Объект 430У": "object-430u",
  "Т-22 ср.": "t-22-medium",
  "Т-62А": "t-62a",
  "Объект 140": "object-140",
  "К-91": "k-91",
  "ИС-4": "is-4",
  "Объект 705А": "object-705a",
  "СТ-II": "st-ii",
  "ИС-7": "is-7",
  "Объект 277": "object-277",
  "Объект 780": "object-780",
  "Объект 279 ранний": "object-279r",
  "Объект 260": "object-260",
  "ИС-7 «Драгун»": "is-7-02",
  "Объект 268 Вариант 4": "object-268-4",
  "Об. 268/4 Морион": "object-268-4-02",
  "Объект 268": "object-268",
  "Объект 268 Вариант 5": "object-730-5",
  "СУ-122В": "su-122v",
  "WT E 100 Оруженосец": "waffentrager-auf-e-100",
  "Объект 261 Вариант 4": "object-261-4-ii",
  "Объект 261": "object-261",

  // Germany
  "Rheinmetall Panzerwagen": "rhm-pzw",
  "LKpz.70 K": "lkpz-70-k",
  "E 50 Ausf. M": "e-50-ausf-m",
  "Leopard 1": "leopard-1",
  "Erich Konzept I": "erich-konzept-i",
  "Maus": "maus",
  "E 100": "e-100",
  "Pz.Kpfw. VII": "pzkpfw-vii",
  "VK 72.01 (K)": "vk-72-01-k",
  "Kampfpanzer 07 P(E)": "kpz-07p-e",
  "Jagdpanzer E 100": "jagdpz-e-100",
  "Grille 15": "grille-15",
  "Waffenträger auf E 100": "waffentrager-auf-e-100",
  "G.W. E 100": "g-w-e-100",

  // USA
  "XM551 Sheridan": "xm551-sheridan",
  "M48A5 Patton": "m48a5-patton",
  "M60": "m60",
  "T95E6": "t95e6",
  "T110E5": "t110e5",
  "M-V-Y": "m-v-y",
  "T57 Heavy Tank": "t57-heavy",
  "H-3": "h-3",
  "Astron-FL": "astron-fl",
  "Grayhound": "grayhound",
  "T110E4": "t110e4",
  "T110E3": "t110e3",
  "XM57": "xm57",
  "T92 HMC": "t92-hmc",

  // China
  "WZ-132-1": "wz-132-1",
  "121": "121",
  "121B": "121b",
  "Царь обезьян": "121b-mk",
  "DZT-159": "dzt-159",
  "BZ-75": "bz-75",
  "116-F3": "wz-122-6-f3",
  "113": "113",
  "WZ-111 model 5A": "wz-111-model-5a",
  "BZ-74-1": "bz-74-1",
  "WZ-111 Qilin": "wz-111-qilin",
  "113 Beijing Opera": "113-beijing-opera",
  "GPT-75": "gpt-75",
  "WZ-113G FT": "wz-113g-ft",
  "114 SP2": "114-sp2",

  // France
  "AMX 13 105": "amx-13-105",
  "Panhard EBR 105": "panhard-ebr-105",
  "Bat.-Châtillon 25 t": "bat-chatillon-25t",
  "Projet Murat": "projet-murat",
  "AMX 30 B": "amx-30-b",
  "AMX M4 mle. 54": "amx-m4-mle-1949-ter",
  "AMX 50 B": "amx-50b",
  "AMX 50 Foch (155)": "amx-50-foch-155",
  "AMX 50 Foch B": "amx-50-foch-b",
  "120 AC «Gendarme»": "schneider-120-ac-gendarme",
  "Bat.-Châtillon 155 58": "bat-chatillon-155-58",

  // UK
  "Manticore": "manticore",
  "Nemesis": "nemesis",
  "Centurion Action X": "centurion-action-x",
  "Concept No. 5": "concept-no-5",
  "FV215b": "fv215b",
  "Super Conqueror": "super-conqueror",
  "T95/FV4201 Chieftain": "t95-fv4201-chieftain",
  "FV217 Badger": "fv217-badger",
  "FV215b (183)": "fv215b-183",
  "FV4005 Stage II": "fv4005",
  "Badger": "badger",
  "Conqueror Gun Carriage": "conqueror-gc",

  // Japan
  "STB-1": "stb-1",
  "Type 5 Heavy": "type-5-heavy",
  "Type 71": "type-71",
  "Ho-Ri 3": "ho-ri-3",

  // Czech
  "TVP T 50/51": "tvp-t-50-51",
  "Vz. 55": "vz-55",
  "Gothic Warrior": "gothic-warrior",

  // Sweden
  "UDES 15/16": "udes-15-16",
  "Kranvagn": "kranvagn",
  "Strv 103B": "strv-103b",

  // Poland
  "CS-63": "cs-63",
  "CS-63 Wilk": "cs-63-wilk",
  "Czołg Pancernik wz.46": "czolg-p-wz-46",
  "Czołг (P) wz.46 Wiedźmak": "czolg-p-wz-46-verbesserter",
  "60TP Lewandowskiego": "60tp-lewandowskiego",
  "Wz.70 Żubr": "nc-wz-70t",

  // Italy
  "Lion": "lion",
  "Progetto M40 mod. 65": "progetto-m40-mod-65",
  "Carro da Combattimento 45 t": "carro-combattimento-45t",
  "Orso": "orso",
  "Rinoceronte": "rinoceronte",
  "Controcarro 3 Minotauro": "cc-3",

  // International
  "T-54D": "t54d",
  "Merkava LP": "merkava-lp"
};

// Карта стран для флагов
export const COUNTRY_FLAG_MAP = {
  // Заглавные буквы (как в данных танков)
  'International': 'intunion',
  'USA': 'usa',
  'USSR': 'ussr',
  'Germany': 'germany',
  'UK': 'uk',
  'France': 'france',
  'Japan': 'japan',
  'China': 'china',
  'Czech': 'czech',
  'Sweden': 'sweden',
  'Poland': 'poland',
  'Italy': 'italy',
  // Нижний регистр (для совместимости)
  'international': 'intunion',
  'usa': 'usa',
  'ussr': 'ussr',
  'germany': 'germany',
  'uk': 'uk',
  'france': 'france',
  'japan': 'japan',
  'china': 'china',
  'czech': 'czech',
  'sweden': 'sweden',
  'poland': 'poland',
  'italy': 'italy'
};



// Карта ролей танков
export const ROLE_ICON_MAP = {
  'Универсальный': 'universal',
  'Снайперский': 'sniper',
  'Поддержки': 'support',
  'Штурмовой': 'assault',
  'Прорыва': 'break',
  'Колёсный': 'wheeled'
};

// Функции для получения путей к изображениям
export function getTankIconPath(tankName) {
  const iconName = TANK_NAME_TO_ICON_MAP[tankName];
  if (iconName) {
    return `src/assets/images/tanks/${iconName}.webp`;
  }

  // Fallback - преобразование имени
  const cleanName = tankName
    .toLowerCase()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]/g, '')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');

  return `src/assets/images/tanks/${cleanName}.webp`;
}

export function getFlagPath(country) {
  // Сначала пробуем точное соответствие, затем нижний регистр
  const flagName = COUNTRY_FLAG_MAP[country] || COUNTRY_FLAG_MAP[country.toLowerCase()] || country.toLowerCase();
  return `src/assets/images/flags/${flagName}.webp`;
}

export function getBlurredFlagPath(country) {
  let countryKey = (country || '').toLowerCase();
  countryKey = COUNTRY_IMAGE_ALIAS[countryKey] || countryKey;
  return `src/assets/images/flags/${countryKey}_blur.webp`;
}

export function getRoleIconPath(roleName) {
  const iconName = ROLE_ICON_MAP[roleName] || 'universal';
  return `src/assets/images/role/${iconName}.png`;
}

export function getTankTypeClass(tankType) {
  return TANK_TYPE_TO_CLASS_SUFFIX[tankType] || '';
}
